"""
لوحة تحكم مراقبة استخدام API
واجهة شاملة لمراقبة استخدام API والإحصائيات المفصلة
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
import json

logger = logging.getLogger(__name__)

class APIMonitoringDashboard:
    """لوحة تحكم مراقبة API"""
    
    def __init__(self, smart_rate_limiter=None, automatic_news_scheduler=None, 
                 intelligent_news_cache=None, db=None):
        self.smart_rate_limiter = smart_rate_limiter
        self.automatic_news_scheduler = automatic_news_scheduler
        self.intelligent_news_cache = intelligent_news_cache
        self.db = db
    
    async def show_main_dashboard(self, update: Update, context: CallbackContext):
        """عرض لوحة التحكم الرئيسية"""
        try:
            user_id = str(update.effective_user.id)
            
            # التحقق من صلاحيات المطور
            from src.config import DEVELOPER_ID
            if user_id != DEVELOPER_ID:
                await update.callback_query.answer("⛔️ هذه الوظيفة متاحة للمطور فقط")
                return
            
            # جمع الإحصائيات
            dashboard_data = await self._collect_dashboard_data()
            
            # تنسيق الرسالة
            message_text = self._format_dashboard_message(dashboard_data)
            
            # إنشاء الأزرار
            keyboard = self._create_dashboard_keyboard()
            
            if update.callback_query:
                await update.callback_query.edit_message_text(
                    text=message_text,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    text=message_text,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                
        except Exception as e:
            logger.error(f"خطأ في عرض لوحة التحكم: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ في تحميل لوحة التحكم")
    
    async def show_api_usage_details(self, update: Update, context: CallbackContext):
        """عرض تفاصيل استخدام API"""
        try:
            platform = context.user_data.get('selected_platform', 'all')
            
            if platform == 'all':
                usage_data = await self._get_all_platforms_usage()
            else:
                usage_data = await self._get_platform_usage_details(platform)
            
            message_text = self._format_api_usage_message(usage_data, platform)
            keyboard = self._create_api_usage_keyboard()
            
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض تفاصيل API: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ في تحميل البيانات")
    
    async def show_cache_statistics(self, update: Update, context: CallbackContext):
        """عرض إحصائيات التخزين المؤقت"""
        try:
            if not self.intelligent_news_cache:
                await update.callback_query.answer("❌ نظام التخزين المؤقت غير متوفر")
                return
            
            cache_stats = await self.intelligent_news_cache.get_cache_stats()
            cache_health = await self.intelligent_news_cache.get_cache_health()
            
            message_text = self._format_cache_stats_message(cache_stats, cache_health)
            keyboard = self._create_cache_stats_keyboard()
            
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات التخزين المؤقت: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ في تحميل الإحصائيات")
    
    async def show_scheduler_status(self, update: Update, context: CallbackContext):
        """عرض حالة المجدول"""
        try:
            if not self.automatic_news_scheduler:
                await update.callback_query.answer("❌ نظام الجدولة غير متوفر")
                return
            
            scheduler_status = self.automatic_news_scheduler.get_scheduler_status()
            
            message_text = self._format_scheduler_status_message(scheduler_status)
            keyboard = self._create_scheduler_keyboard()
            
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض حالة المجدول: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ في تحميل حالة المجدول")
    
    async def _collect_dashboard_data(self) -> Dict[str, Any]:
        """جمع بيانات لوحة التحكم"""
        data = {
            'timestamp': datetime.now(),
            'api_usage': {},
            'cache_stats': {},
            'scheduler_status': {},
            'system_health': 'unknown'
        }
        
        # جمع بيانات استخدام API
        if self.smart_rate_limiter:
            platforms = ['binance', 'coindesk', 'coingecko', 'gemini']
            for platform in platforms:
                try:
                    platform_status = await self.smart_rate_limiter.get_platform_status(platform)
                    data['api_usage'][platform] = platform_status
                except Exception as e:
                    logger.warning(f"خطأ في جلب بيانات {platform}: {str(e)}")
        
        # جمع بيانات التخزين المؤقت
        if self.intelligent_news_cache:
            try:
                data['cache_stats'] = await self.intelligent_news_cache.get_cache_stats()
            except Exception as e:
                logger.warning(f"خطأ في جلب إحصائيات التخزين المؤقت: {str(e)}")
        
        # جمع بيانات المجدول
        if self.automatic_news_scheduler:
            try:
                data['scheduler_status'] = self.automatic_news_scheduler.get_scheduler_status()
            except Exception as e:
                logger.warning(f"خطأ في جلب حالة المجدول: {str(e)}")
        
        # تقييم صحة النظام
        data['system_health'] = self._evaluate_system_health(data)
        
        return data
    
    def _format_dashboard_message(self, data: Dict[str, Any]) -> str:
        """تنسيق رسالة لوحة التحكم"""
        message = "🖥️ <b>لوحة تحكم مراقبة API</b>\n\n"
        
        # حالة النظام العامة
        health_emoji = {
            'excellent': '🟢',
            'good': '🟡', 
            'fair': '🟠',
            'poor': '🔴',
            'unknown': '⚪'
        }
        
        message += f"🏥 <b>صحة النظام:</b> {health_emoji.get(data['system_health'], '⚪')} {data['system_health'].title()}\n\n"
        
        # ملخص استخدام API
        if data['api_usage']:
            message += "📊 <b>ملخص استخدام API:</b>\n"
            for platform, status in data['api_usage'].items():
                if status:
                    usage_pct = status.get('usage_percentage', {}).get('minute', 0)
                    health_status = status.get('status', 'unknown')
                    message += f"• {platform.title()}: {usage_pct:.1f}% ({health_status})\n"
            message += "\n"
        
        # إحصائيات التخزين المؤقت
        if data['cache_stats']:
            cache_stats = data['cache_stats']
            message += f"💾 <b>التخزين المؤقت:</b>\n"
            message += f"• الكفاءة: {cache_stats.get('cache_efficiency', 0):.1f}%\n"
            message += f"• الذاكرة: {cache_stats.get('memory_usage_mb', 0):.1f} MB\n"
            message += f"• المدخلات: {cache_stats.get('total_entries', 0)}\n\n"
        
        # حالة المجدول
        if data['scheduler_status']:
            scheduler = data['scheduler_status']
            message += f"⏰ <b>المجدول:</b>\n"
            message += f"• الحالة: {scheduler.get('status', 'unknown')}\n"
            message += f"• المهام: {scheduler.get('total_jobs', 0)}\n"
            message += f"• المشتركون: {scheduler.get('subscribers_count', 0)}\n\n"
        
        message += f"🕐 آخر تحديث: {data['timestamp'].strftime('%H:%M:%S')}"
        
        return message
    
    def _create_dashboard_keyboard(self) -> InlineKeyboardMarkup:
        """إنشاء لوحة مفاتيح لوحة التحكم"""
        keyboard = [
            [
                InlineKeyboardButton("📊 تفاصيل API", callback_data="api_details"),
                InlineKeyboardButton("💾 التخزين المؤقت", callback_data="cache_stats")
            ],
            [
                InlineKeyboardButton("⏰ المجدول", callback_data="scheduler_status"),
                InlineKeyboardButton("📈 التحليلات", callback_data="analytics")
            ],
            [
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh_dashboard"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="dashboard_settings")
            ],
            [
                InlineKeyboardButton("🔙 العودة", callback_data="main_menu")
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    def _create_api_usage_keyboard(self) -> InlineKeyboardMarkup:
        """إنشاء لوحة مفاتيح تفاصيل API"""
        keyboard = [
            [
                InlineKeyboardButton("📊 Binance", callback_data="api_platform_binance"),
                InlineKeyboardButton("📰 CoinDesk", callback_data="api_platform_coindesk")
            ],
            [
                InlineKeyboardButton("🦎 CoinGecko", callback_data="api_platform_coingecko"),
                InlineKeyboardButton("🤖 Gemini AI", callback_data="api_platform_gemini")
            ],
            [
                InlineKeyboardButton("📈 تحليلات الاستخدام", callback_data="usage_analytics"),
                InlineKeyboardButton("🔧 تحسين التوزيع", callback_data="optimize_distribution")
            ],
            [
                InlineKeyboardButton("🔙 العودة", callback_data="dashboard_main")
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    def _create_cache_stats_keyboard(self) -> InlineKeyboardMarkup:
        """إنشاء لوحة مفاتيح إحصائيات التخزين المؤقت"""
        keyboard = [
            [
                InlineKeyboardButton("🧹 تنظيف التخزين", callback_data="cache_cleanup"),
                InlineKeyboardButton("🔧 تحسين التخزين", callback_data="cache_optimize")
            ],
            [
                InlineKeyboardButton("📊 تقرير الصحة", callback_data="cache_health"),
                InlineKeyboardButton("⚙️ إعدادات التخزين", callback_data="cache_settings")
            ],
            [
                InlineKeyboardButton("🔙 العودة", callback_data="dashboard_main")
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    def _create_scheduler_keyboard(self) -> InlineKeyboardMarkup:
        """إنشاء لوحة مفاتيح المجدول"""
        keyboard = [
            [
                InlineKeyboardButton("▶️ تشغيل", callback_data="scheduler_start"),
                InlineKeyboardButton("⏸️ إيقاف", callback_data="scheduler_stop")
            ],
            [
                InlineKeyboardButton("📋 قائمة المهام", callback_data="scheduler_jobs"),
                InlineKeyboardButton("👥 المشتركون", callback_data="scheduler_subscribers")
            ],
            [
                InlineKeyboardButton("⚙️ إعدادات الجدولة", callback_data="scheduler_settings"),
                InlineKeyboardButton("📊 إحصائيات", callback_data="scheduler_stats")
            ],
            [
                InlineKeyboardButton("🔙 العودة", callback_data="dashboard_main")
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    def _evaluate_system_health(self, data: Dict[str, Any]) -> str:
        """تقييم صحة النظام العامة"""
        health_score = 100
        
        # تقييم استخدام API
        if data['api_usage']:
            for platform, status in data['api_usage'].items():
                if status and status.get('status') == 'poor':
                    health_score -= 20
                elif status and status.get('status') == 'fair':
                    health_score -= 10
        
        # تقييم التخزين المؤقت
        if data['cache_stats']:
            cache_efficiency = data['cache_stats'].get('cache_efficiency', 0)
            if cache_efficiency < 50:
                health_score -= 15
            elif cache_efficiency < 70:
                health_score -= 5
        
        # تقييم المجدول
        if data['scheduler_status']:
            if data['scheduler_status'].get('status') != 'running':
                health_score -= 25
        
        # تحديد التقييم النهائي
        if health_score >= 90:
            return 'excellent'
        elif health_score >= 75:
            return 'good'
        elif health_score >= 60:
            return 'fair'
        else:
            return 'poor'

    async def _get_all_platforms_usage(self) -> Dict[str, Any]:
        """الحصول على استخدام جميع المنصات"""
        if not self.smart_rate_limiter:
            return {}

        platforms_data = {}
        platforms = ['binance', 'coindesk', 'coingecko', 'gemini']

        for platform in platforms:
            try:
                platform_status = await self.smart_rate_limiter.get_platform_status(platform)
                platforms_data[platform] = platform_status
            except Exception as e:
                logger.warning(f"خطأ في جلب بيانات {platform}: {str(e)}")
                platforms_data[platform] = None

        return platforms_data

    async def _get_platform_usage_details(self, platform: str) -> Dict[str, Any]:
        """الحصول على تفاصيل استخدام منصة محددة"""
        if not self.smart_rate_limiter:
            return {}

        try:
            platform_status = await self.smart_rate_limiter.get_platform_status(platform)
            analytics = await self.smart_rate_limiter.get_usage_analytics(platform, days=7)
            optimization = await self.smart_rate_limiter.optimize_request_distribution(platform)

            return {
                'status': platform_status,
                'analytics': analytics,
                'optimization': optimization
            }
        except Exception as e:
            logger.error(f"خطأ في جلب تفاصيل {platform}: {str(e)}")
            return {}

    def _format_api_usage_message(self, usage_data: Dict[str, Any], platform: str) -> str:
        """تنسيق رسالة استخدام API"""
        if platform == 'all':
            message = "📊 <b>استخدام جميع منصات API</b>\n\n"

            for platform_name, data in usage_data.items():
                if data:
                    message += f"<b>{platform_name.title()}:</b>\n"
                    usage_pct = data.get('usage_percentage', {})
                    message += f"• الدقيقة: {usage_pct.get('minute', 0):.1f}%\n"
                    message += f"• الساعة: {usage_pct.get('hour', 0):.1f}%\n"
                    message += f"• اليوم: {usage_pct.get('day', 0):.1f}%\n"
                    message += f"• الحالة: {data.get('status', 'unknown')}\n\n"
                else:
                    message += f"<b>{platform_name.title()}:</b> ❌ غير متوفر\n\n"
        else:
            data = usage_data.get('status', {})
            message = f"📊 <b>تفاصيل {platform.title()} API</b>\n\n"

            if data:
                # الحدود والاستخدام
                limits = data.get('limits', {})
                current = data.get('current_usage', {})
                remaining = data.get('remaining', {})

                message += "<b>الحدود والاستخدام:</b>\n"
                message += f"• الحد الأقصى/دقيقة: {limits.get('per_minute', 0)}\n"
                message += f"• المستخدم/دقيقة: {current.get('minute', 0)}\n"
                message += f"• المتبقي/دقيقة: {remaining.get('minute', 0)}\n\n"

                message += f"• الحد الأقصى/ساعة: {limits.get('per_hour', 0)}\n"
                message += f"• المستخدم/ساعة: {current.get('hour', 0)}\n"
                message += f"• المتبقي/ساعة: {remaining.get('hour', 0)}\n\n"

                # الأداء
                performance = data.get('performance', {})
                message += "<b>الأداء:</b>\n"
                message += f"• إجمالي الطلبات: {performance.get('total_requests', 0)}\n"
                message += f"• الطلبات الناجحة: {performance.get('successful_requests', 0)}\n"
                message += f"• الطلبات الفاشلة: {performance.get('failed_requests', 0)}\n"
                message += f"• متوسط وقت الاستجابة: {performance.get('average_response_time', 0):.2f}ms\n"
                message += f"• نقاط الكفاءة: {performance.get('efficiency_score', 0):.1f}/100\n\n"

                # التحليلات
                analytics = usage_data.get('analytics', {})
                if analytics:
                    message += "<b>تحليلات الأسبوع الماضي:</b>\n"
                    message += f"• إجمالي الطلبات: {analytics.get('total_requests', 0)}\n"
                    message += f"• معدل النجاح: {(analytics.get('successful_requests', 0) / max(analytics.get('total_requests', 1), 1) * 100):.1f}%\n"

                    peak_hours = analytics.get('peak_hours', [])
                    if peak_hours:
                        message += f"• ساعات الذروة: {', '.join(peak_hours[:3])}\n"
            else:
                message += "❌ لا توجد بيانات متوفرة"

        return message

    def _format_cache_stats_message(self, cache_stats: Dict[str, Any],
                                   cache_health: Dict[str, Any]) -> str:
        """تنسيق رسالة إحصائيات التخزين المؤقت"""
        message = "💾 <b>إحصائيات التخزين المؤقت</b>\n\n"

        # الصحة العامة
        health_emoji = {
            'ممتاز': '🟢',
            'جيد': '🟡',
            'متوسط': '🟠',
            'ضعيف': '🔴'
        }

        health_status = cache_health.get('health_status', 'unknown')
        health_score = cache_health.get('health_score', 0)

        message += f"🏥 <b>الصحة:</b> {health_emoji.get(health_status, '⚪')} {health_status} ({health_score}/100)\n\n"

        # الإحصائيات الأساسية
        message += "<b>الإحصائيات الأساسية:</b>\n"
        message += f"• الكفاءة: {cache_stats.get('cache_efficiency', 0):.1f}%\n"
        message += f"• الإصابات: {cache_stats.get('hits', 0)}\n"
        message += f"• الإخفاقات: {cache_stats.get('misses', 0)}\n"
        message += f"• الطرد: {cache_stats.get('evictions', 0)}\n\n"

        # استخدام الذاكرة
        message += "<b>استخدام الذاكرة:</b>\n"
        message += f"• المستخدم: {cache_stats.get('memory_usage_mb', 0):.1f} MB\n"
        message += f"• الحد الأقصى: {cache_stats.get('max_size_mb', 0):.1f} MB\n"
        message += f"• النسبة: {cache_health.get('memory_usage_percent', 0):.1f}%\n\n"

        # المدخلات
        message += "<b>المدخلات:</b>\n"
        message += f"• إجمالي المدخلات: {cache_stats.get('total_entries', 0)}\n"

        entries_by_source = cache_stats.get('entries_by_source', {})
        if entries_by_source:
            message += "• حسب المصدر:\n"
            for source, count in entries_by_source.items():
                message += f"  - {source}: {count}\n"

        # التوصيات
        recommendations = cache_health.get('recommendations', [])
        if recommendations:
            message += "\n<b>💡 التوصيات:</b>\n"
            for rec in recommendations[:3]:  # أول 3 توصيات
                message += f"• {rec}\n"

        return message

    def _format_scheduler_status_message(self, scheduler_status: Dict[str, Any]) -> str:
        """تنسيق رسالة حالة المجدول"""
        message = "⏰ <b>حالة المجدول</b>\n\n"

        status = scheduler_status.get('status', 'unknown')
        status_emoji = {
            'running': '🟢',
            'stopped': '🔴',
            'not_initialized': '⚪'
        }

        message += f"📊 <b>الحالة:</b> {status_emoji.get(status, '⚪')} {status.title()}\n\n"

        # المهام
        total_jobs = scheduler_status.get('total_jobs', 0)
        message += f"📋 <b>المهام:</b> {total_jobs}\n"

        jobs = scheduler_status.get('jobs', [])
        if jobs:
            message += "<b>قائمة المهام:</b>\n"
            for job in jobs[:5]:  # أول 5 مهام
                name = job.get('name', 'مهمة غير معروفة')
                next_run = job.get('next_run', 'غير محدد')
                message += f"• {name}\n  التشغيل التالي: {next_run}\n"

        # المشتركون
        subscribers_count = scheduler_status.get('subscribers_count', 0)
        message += f"\n👥 <b>المشتركون:</b> {subscribers_count}\n"

        # استخدام API
        api_usage = scheduler_status.get('api_usage', {})
        if api_usage:
            message += "\n<b>استخدام API:</b>\n"
            for platform, usage in api_usage.items():
                remaining = usage.get('remaining', 0)
                message += f"• {platform.title()}: {remaining} متبقي\n"

        return message

# معالجات الأحداث
async def handle_dashboard_callback(update: Update, context: CallbackContext):
    """معالج أحداث لوحة التحكم"""
    if not api_monitoring_dashboard:
        await update.callback_query.answer("❌ لوحة التحكم غير متوفرة")
        return

    query = update.callback_query
    data = query.data

    try:
        if data == "dashboard_main":
            await api_monitoring_dashboard.show_main_dashboard(update, context)
        elif data == "api_details":
            await api_monitoring_dashboard.show_api_usage_details(update, context)
        elif data == "cache_stats":
            await api_monitoring_dashboard.show_cache_statistics(update, context)
        elif data == "scheduler_status":
            await api_monitoring_dashboard.show_scheduler_status(update, context)
        elif data == "refresh_dashboard":
            await api_monitoring_dashboard.show_main_dashboard(update, context)
            await query.answer("🔄 تم تحديث البيانات")
        elif data.startswith("api_platform_"):
            platform = data.replace("api_platform_", "")
            context.user_data['selected_platform'] = platform
            await api_monitoring_dashboard.show_api_usage_details(update, context)
        else:
            await query.answer("🚧 هذه الوظيفة قيد التطوير")

    except Exception as e:
        logger.error(f"خطأ في معالجة حدث لوحة التحكم: {str(e)}")
        await query.answer("❌ حدث خطأ في معالجة الطلب")

# إنشاء نسخة عامة من لوحة التحكم
api_monitoring_dashboard = None

def initialize_api_monitoring_dashboard(smart_rate_limiter=None, automatic_news_scheduler=None,
                                      intelligent_news_cache=None, db=None):
    """تهيئة لوحة تحكم مراقبة API"""
    global api_monitoring_dashboard
    api_monitoring_dashboard = APIMonitoringDashboard(
        smart_rate_limiter, automatic_news_scheduler, intelligent_news_cache, db
    )
    logger.info("✅ تم تهيئة لوحة تحكم مراقبة API")
    return api_monitoring_dashboard
