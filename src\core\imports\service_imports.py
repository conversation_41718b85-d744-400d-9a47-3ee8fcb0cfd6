"""
🔧 استيرادات خدمات النظام
========================

جميع خدمات النظام الداخلية والوحدات المحلية.
هذه الخدمات تدير الوظائف الأساسية للبوت.

الفئات:
- خدمات المعاملات المالية
- خدمات التنبيهات
- خدمات إدارة المستخدمين
- خدمات الإدارة والنسخ الاحتياطي
- خدمات معالجة الأخطاء
- خدمات إدارة API
- خدمات النظام العامة
"""

# ===== خدمات المعاملات المالية =====
try:
    from services import (
        create_payment_transaction,
        update_transaction_with_binance_id,
        cancel_transaction,
        verify_payment_transaction,
        verify_paypal_transaction,
        extend_transaction,
        complete_payment,
        verify_payment,
        cleanup_pending_transactions,
        notify_expiring_transactions,
        send_transaction_expiry_notification,
        get_transaction_manager,
        initialize_transaction_manager,
        load_user_settings,
        save_user_settings,
        set_data_manager,
        initialize_transaction_service
    )
    TRANSACTION_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات المعاملات: {e}")
    TRANSACTION_SERVICES_AVAILABLE = False

# ===== خدمات التنبيهات =====
try:
    from services.alert_service import (
        initialize_alert_service,
        alert_command,
        setup_price_alert,
        handle_custom_alert,
        process_custom_alert,
        check_alerts,
        send_daily_report
    )
    ALERT_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات التنبيهات: {e}")
    ALERT_SERVICES_AVAILABLE = False

# ===== خدمات معالجة المدفوعات =====
try:
    from services.handle_paypal_payment import handle_paypal_payment
    from services.handle_payment_verification import handle_payment_verification
    PAYMENT_HANDLERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد معالجات المدفوعات: {e}")
    PAYMENT_HANDLERS_AVAILABLE = False

# ===== خدمات النظام المجاني =====
try:
    from services.free_day_system import free_day_system
    from services.automatic_payment_verification import AutomaticPaymentVerifier
    FREE_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد نظام الأيام المجانية: {e}")
    FREE_SYSTEM_AVAILABLE = False

# ===== خدمات الإعدادات =====
try:
    from services.system_settings import system_settings
    SYSTEM_SETTINGS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد إعدادات النظام: {e}")
    SYSTEM_SETTINGS_AVAILABLE = False

# ===== خدمات معالجة الأخطاء =====
try:
    from services.error_handler import (
        initialize_error_handler,
        telegram_error_handler,
        log_error,
        log_info,
        log_warning,
        log_debug,
        specialized_handlers,
        safe_send_message,
        safe_edit_message,
        safe_delete_message,
        generate_error_report
    )
    ERROR_HANDLER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد معالج الأخطاء: {e}")
    ERROR_HANDLER_AVAILABLE = False

# ===== خدمات الإدارة =====
try:
    from services.admin_service import (
        initialize_admin_service,
        cast,
        ban_user,
        unban_user,
        system_info,
        cleanup_system,
        backup_data,
        grant_free_day_command,
        stop_all_scheduled_tasks
    )
    ADMIN_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات الإدارة: {e}")
    ADMIN_SERVICES_AVAILABLE = False

# ===== خدمات إدارة المستخدمين =====
try:
    from services.user_management import (
        initialize_user_management,
        start,
        add_user_to_users_collection,
        show_user_stats,
        check_expired_subscriptions,
        notify_expiring_subscriptions,
        _update_expired_subscription,
        _send_expiry_notification,
        activate_subscription,
        manage_free_day_settings,
        set_free_day,
        stop
    )
    USER_MANAGEMENT_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات إدارة المستخدمين: {e}")
    USER_MANAGEMENT_AVAILABLE = False

# ===== خدمات النسخ الاحتياطي =====
try:
    from services.backup_service import (
        initialize_backup_service,
        get_backup_instances,
        perform_backup,
        backup_subscription_data
    )
    BACKUP_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات النسخ الاحتياطي: {e}")
    BACKUP_SERVICES_AVAILABLE = False

# ===== خدمات إدارة API =====
try:
    from services.api_management import (
        initialize_api_management_service,
        api_setup_command,
        api_info_command,
        delete_api_command
    )
    API_MANAGEMENT_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد خدمات إدارة API: {e}")
    API_MANAGEMENT_AVAILABLE = False

# تجميع جميع الخدمات
all_service_functions = {
    'transaction_services': {
        'available': TRANSACTION_SERVICES_AVAILABLE,
        'functions': [
            'create_payment_transaction', 'update_transaction_with_binance_id',
            'cancel_transaction', 'verify_payment_transaction', 'verify_paypal_transaction',
            'extend_transaction', 'complete_payment', 'verify_payment',
            'cleanup_pending_transactions', 'notify_expiring_transactions',
            'send_transaction_expiry_notification', 'get_transaction_manager',
            'initialize_transaction_manager', 'load_user_settings', 'save_user_settings',
            'set_data_manager', 'initialize_transaction_service'
        ]
    },
    'alert_services': {
        'available': ALERT_SERVICES_AVAILABLE,
        'functions': [
            'initialize_alert_service', 'alert_command', 'setup_price_alert',
            'handle_custom_alert', 'process_custom_alert', 'check_alerts', 'send_daily_report'
        ]
    },
    'payment_handlers': {
        'available': PAYMENT_HANDLERS_AVAILABLE,
        'functions': ['handle_paypal_payment', 'handle_payment_verification']
    },
    'free_system': {
        'available': FREE_SYSTEM_AVAILABLE,
        'functions': ['free_day_system', 'AutomaticPaymentVerifier']
    },
    'system_settings': {
        'available': SYSTEM_SETTINGS_AVAILABLE,
        'functions': ['system_settings']
    },
    'error_handler': {
        'available': ERROR_HANDLER_AVAILABLE,
        'functions': [
            'initialize_error_handler', 'telegram_error_handler', 'log_error',
            'log_info', 'log_warning', 'log_debug', 'specialized_handlers',
            'safe_send_message', 'safe_edit_message', 'safe_delete_message', 'generate_error_report'
        ]
    },
    'admin_services': {
        'available': ADMIN_SERVICES_AVAILABLE,
        'functions': [
            'initialize_admin_service', 'cast', 'ban_user', 'unban_user',
            'system_info', 'cleanup_system', 'backup_data', 'grant_free_day_command',
            'stop_all_scheduled_tasks'
        ]
    },
    'user_management': {
        'available': USER_MANAGEMENT_AVAILABLE,
        'functions': [
            'initialize_user_management', 'start', 'add_user_to_users_collection',
            'show_user_stats', 'check_expired_subscriptions', 'notify_expiring_subscriptions',
            '_update_expired_subscription', '_send_expiry_notification', 'activate_subscription',
            'manage_free_day_settings', 'set_free_day', 'stop'
        ]
    },
    'backup_services': {
        'available': BACKUP_SERVICES_AVAILABLE,
        'functions': [
            'initialize_backup_service', 'get_backup_instances', 'perform_backup', 'backup_subscription_data'
        ]
    },
    'api_management': {
        'available': API_MANAGEMENT_AVAILABLE,
        'functions': [
            'initialize_api_management_service', 'api_setup_command', 'api_info_command', 'delete_api_command'
        ]
    }
}

__all__ = ['all_service_functions'] + [
    f"{category}_AVAILABLE" for category in [
        'TRANSACTION_SERVICES', 'ALERT_SERVICES', 'PAYMENT_HANDLERS', 'FREE_SYSTEM',
        'SYSTEM_SETTINGS', 'ERROR_HANDLER', 'ADMIN_SERVICES', 'USER_MANAGEMENT',
        'BACKUP_SERVICES', 'API_MANAGEMENT'
    ]
]

def get_service_status():
    """
    حالة توفر جميع الخدمات
    
    Returns:
        dict: حالة كل خدمة
    """
    return {
        category: info['available'] 
        for category, info in all_service_functions.items()
    }

def validate_critical_services():
    """
    التحقق من توفر الخدمات الحرجة
    
    Returns:
        tuple: (success: bool, missing_services: list)
    """
    critical_services = [
        'transaction_services', 'error_handler', 'user_management', 'system_settings'
    ]
    
    missing = []
    for service in critical_services:
        if not all_service_functions[service]['available']:
            missing.append(service)
    
    return len(missing) == 0, missing

# اختبار فوري للخدمات
if __name__ == "__main__":
    success, missing = validate_critical_services()
    if success:
        print("✅ جميع الخدمات الحرجة متوفرة")
    else:
        print(f"❌ خدمات حرجة مفقودة: {missing}")
        
    # عرض حالة جميع الخدمات
    status = get_service_status()
    print("\n📊 حالة خدمات النظام:")
    for service, available in status.items():
        status_icon = "✅" if available else "❌"
        print(f"{status_icon} {service}")
