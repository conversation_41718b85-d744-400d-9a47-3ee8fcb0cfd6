"""
تكامل نظام الأخبار التلقائي
ربط جميع الأنظمة الجديدة مع النظام الرئيسي
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class AutomaticNewsIntegration:
    """كلاس تكامل نظام الأخبار التلقائي"""
    
    def __init__(self, db=None, bot=None):
        self.db = db
        self.bot = bot
        
        # مراجع للأنظمة الفرعية
        self.smart_rate_limiter = None
        self.automatic_news_scheduler = None
        self.intelligent_news_cache = None
        self.automatic_news_notifications = None
        self.api_monitoring_dashboard = None
        self.news_system = None
        
        # حالة النظام
        self.is_initialized = False
        self.is_running = False
    
    async def initialize_all_systems(self):
        """تهيئة جميع الأنظمة"""
        try:
            logger.info("🚀 بدء تهيئة نظام الأخبار التلقائي الشامل...")
            
            # تهيئة نظام إدارة معدل الطلبات الذكي
            from services.smart_rate_limiter import initialize_smart_rate_limiter
            self.smart_rate_limiter = initialize_smart_rate_limiter(self.db)
            
            # تهيئة نظام التخزين المؤقت الذكي
            from services.intelligent_news_cache import initialize_intelligent_news_cache
            self.intelligent_news_cache = initialize_intelligent_news_cache(max_size_mb=100, db=self.db)
            
            # تحميل البيانات من قاعدة البيانات
            if self.intelligent_news_cache:
                await self.intelligent_news_cache.load_from_db(max_age_hours=24)
                await self.intelligent_news_cache.start_cleanup_scheduler(interval_minutes=30)
            
            # تهيئة نظام الإشعارات التلقائية
            from services.automatic_news_notifications import initialize_automatic_news_notifications
            self.automatic_news_notifications = initialize_automatic_news_notifications(self.db, self.bot)
            
            # تحميل قواعد المستخدمين
            if self.automatic_news_notifications:
                await self.automatic_news_notifications.load_user_rules()
            
            # تهيئة نظام الأخبار الأساسي
            from services.news_system import initialize_news_system
            gemini_api_key = await self._get_gemini_api_key()
            self.news_system = initialize_news_system(self.db, gemini_api_key)
            
            # تهيئة نظام الجدولة التلقائية
            from services.automatic_news_scheduler import initialize_automatic_news_scheduler
            self.automatic_news_scheduler = initialize_automatic_news_scheduler(
                self.news_system, self.db
            )
            
            # تهيئة لوحة تحكم مراقبة API
            from handlers.api_monitoring_dashboard import initialize_api_monitoring_dashboard
            self.api_monitoring_dashboard = initialize_api_monitoring_dashboard(
                self.smart_rate_limiter,
                self.automatic_news_scheduler,
                self.intelligent_news_cache,
                self.db
            )
            
            self.is_initialized = True
            logger.info("✅ تم تهيئة جميع أنظمة الأخبار التلقائية بنجاح")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة الأنظمة: {str(e)}")
            return False
    
    async def start_automatic_systems(self):
        """بدء تشغيل الأنظمة التلقائية"""
        if not self.is_initialized:
            logger.error("❌ يجب تهيئة الأنظمة أولاً")
            return False
        
        try:
            logger.info("🚀 بدء تشغيل الأنظمة التلقائية...")
            
            # بدء نظام الجدولة التلقائية
            if self.automatic_news_scheduler:
                await self.automatic_news_scheduler.start()
            
            self.is_running = True
            logger.info("✅ تم بدء تشغيل جميع الأنظمة التلقائية")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء الأنظمة: {str(e)}")
            return False
    
    async def stop_automatic_systems(self):
        """إيقاف الأنظمة التلقائية"""
        try:
            logger.info("⏹️ إيقاف الأنظمة التلقائية...")
            
            # إيقاف نظام الجدولة
            if self.automatic_news_scheduler:
                await self.automatic_news_scheduler.stop()
            
            # إيقاف تنظيف التخزين المؤقت
            if self.intelligent_news_cache:
                await self.intelligent_news_cache.stop_cleanup_scheduler()
            
            self.is_running = False
            logger.info("✅ تم إيقاف جميع الأنظمة التلقائية")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف الأنظمة: {str(e)}")
    
    async def get_system_health(self) -> Dict[str, Any]:
        """الحصول على صحة النظام الشاملة"""
        health_data = {
            'overall_status': 'unknown',
            'systems': {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # فحص نظام إدارة معدل الطلبات
            if self.smart_rate_limiter:
                platforms = ['binance', 'coindesk', 'coingecko', 'gemini']
                api_health = {}
                for platform in platforms:
                    try:
                        status = await self.smart_rate_limiter.get_platform_status(platform)
                        api_health[platform] = status.get('status', 'unknown') if status else 'offline'
                    except:
                        api_health[platform] = 'error'
                
                health_data['systems']['rate_limiter'] = {
                    'status': 'online',
                    'platforms': api_health
                }
            
            # فحص نظام التخزين المؤقت
            if self.intelligent_news_cache:
                try:
                    cache_health = await self.intelligent_news_cache.get_cache_health()
                    health_data['systems']['cache'] = {
                        'status': 'online',
                        'health_score': cache_health.get('health_score', 0),
                        'health_status': cache_health.get('health_status', 'unknown')
                    }
                except:
                    health_data['systems']['cache'] = {'status': 'error'}
            
            # فحص نظام الجدولة
            if self.automatic_news_scheduler:
                try:
                    scheduler_status = self.automatic_news_scheduler.get_scheduler_status()
                    health_data['systems']['scheduler'] = {
                        'status': scheduler_status.get('status', 'unknown'),
                        'jobs_count': scheduler_status.get('total_jobs', 0),
                        'subscribers': scheduler_status.get('subscribers_count', 0)
                    }
                except:
                    health_data['systems']['scheduler'] = {'status': 'error'}
            
            # فحص نظام الإشعارات
            if self.automatic_news_notifications:
                try:
                    notif_stats = await self.automatic_news_notifications.get_notification_stats()
                    health_data['systems']['notifications'] = {
                        'status': 'online',
                        'active_users': notif_stats.get('active_users', 0),
                        'success_rate': notif_stats.get('success_rate', 0)
                    }
                except:
                    health_data['systems']['notifications'] = {'status': 'error'}
            
            # تحديد الحالة العامة
            system_statuses = []
            for system_data in health_data['systems'].values():
                status = system_data.get('status', 'unknown')
                if status == 'online' or status == 'running':
                    system_statuses.append('good')
                elif status == 'error' or status == 'offline':
                    system_statuses.append('bad')
                else:
                    system_statuses.append('unknown')
            
            if all(s == 'good' for s in system_statuses):
                health_data['overall_status'] = 'excellent'
            elif any(s == 'bad' for s in system_statuses):
                health_data['overall_status'] = 'poor'
            else:
                health_data['overall_status'] = 'fair'
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة النظام: {str(e)}")
            health_data['overall_status'] = 'error'
        
        return health_data
    
    async def process_news_automatically(self):
        """معالجة الأخبار تلقائياً"""
        if not self.is_running:
            return
        
        try:
            # جلب آخر الأخبار
            if self.news_system:
                latest_news = await self.news_system.get_latest_news(limit=10)
                
                if latest_news:
                    # حفظ في التخزين المؤقت
                    if self.intelligent_news_cache:
                        for news_item in latest_news:
                            cache_key = f"news_{news_item.source.value}_{hash(news_item.title)}"
                            await self.intelligent_news_cache.set(
                                key=cache_key,
                                data={
                                    'title': news_item.title,
                                    'content': news_item.content,
                                    'source': news_item.source.value,
                                    'published_at': news_item.published_at.isoformat(),
                                    'url': news_item.url,
                                    'symbols': news_item.symbols or []
                                },
                                priority=2,
                                source='news'
                            )
                    
                    # معالجة للإشعارات
                    if self.automatic_news_notifications:
                        await self.automatic_news_notifications.process_news_for_notifications(latest_news)
                    
                    logger.info(f"تم معالجة {len(latest_news)} خبر تلقائياً")
            
        except Exception as e:
            logger.error(f"خطأ في المعالجة التلقائية للأخبار: {str(e)}")
    
    async def _get_gemini_api_key(self) -> Optional[str]:
        """الحصول على مفتاح Gemini API"""
        try:
            if self.db:
                system_settings = self.db.collection('system_settings').document('config').get()
                if system_settings.exists:
                    settings_data = system_settings.to_dict()
                    return settings_data.get('gemini_api_key')
        except Exception as e:
            logger.warning(f"لم يتم العثور على مفتاح Gemini: {str(e)}")
        
        return None
    
    def get_integration_status(self) -> Dict[str, Any]:
        """الحصول على حالة التكامل"""
        return {
            'initialized': self.is_initialized,
            'running': self.is_running,
            'systems': {
                'rate_limiter': self.smart_rate_limiter is not None,
                'cache': self.intelligent_news_cache is not None,
                'scheduler': self.automatic_news_scheduler is not None,
                'notifications': self.automatic_news_notifications is not None,
                'dashboard': self.api_monitoring_dashboard is not None,
                'news_system': self.news_system is not None
            }
        }

# إنشاء نسخة عامة من نظام التكامل
automatic_news_integration = None

async def initialize_automatic_news_integration(db=None, bot=None):
    """تهيئة نظام التكامل التلقائي"""
    global automatic_news_integration
    automatic_news_integration = AutomaticNewsIntegration(db, bot)
    
    # تهيئة جميع الأنظمة
    success = await automatic_news_integration.initialize_all_systems()
    
    if success:
        logger.info("✅ تم تهيئة نظام التكامل التلقائي بنجاح")
        return automatic_news_integration
    else:
        logger.error("❌ فشل في تهيئة نظام التكامل التلقائي")
        return None
