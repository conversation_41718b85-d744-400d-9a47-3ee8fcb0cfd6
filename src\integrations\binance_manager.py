"""
مدير اتصالات Binance API
"""

import logging
import sys
import aiohttp
import hmac
import hashlib
import time
from datetime import datetime, timedelta
from urllib.parse import urlencode
from typing import Optional
from typing import Optional, List

logger = logging.getLogger(__name__)


class BinanceManager:
    """مدير اتصالات Binance API"""
    __slots__ = ['base_url', 'market_data_cache', 'market_data_expiry', 'cache_timeout', 'db', 'api_manager']

    def __init__(self, db=None):
        # تكوين Event Loop لنظام Windows
        if sys.platform == 'win32':
            import asyncio
            try:
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            except Exception as e:
                logger.warning(f"فشل في تعيين WindowsSelectorEventLoopPolicy: {e}")

        self.base_url = "https://api.binance.com/api/v3"
        self.market_data_cache = {}  # ذاكرة محلية لتخزين بيانات السوق
        self.market_data_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.cache_timeout = 300  # 5 minutes
        self.db = db
        self.api_manager = None  # سيتم تعيينه لاحقاً  # استخدام قاعدة البيانات Firestore

    def _generate_signature(self, params: dict, api_secret: str) -> str:
        """إنشاء توقيع آمن للطلب"""
        query_string = urlencode(params)
        return hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def check_symbol_availability(self, symbol: str) -> bool:
        """التحقق من توفر العملة في Binance"""
        try:
            # تنظيف الرمز وإزالة الفراغات
            original_symbol = symbol
            symbol = symbol.upper().strip()
            logger.info(f"التحقق من توفر الرمز: {original_symbol} -> {symbol}")

            # المحاولة باستخدام الرمز الأصلي أولاً
            url = f"{self.base_url}/exchangeInfo"
            params = {'symbol': symbol}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = data.get('symbols', [])
                        logger.info(f"تم الحصول على {len(symbols)} رمز من Binance للتحقق من {symbol}")

                        for symbol_info in symbols:
                            if symbol_info.get('symbol') == symbol and symbol_info.get('status') == 'TRADING':
                                logger.info(f"✅ الرمز {symbol} متاح للتداول في Binance")
                                return True
                    else:
                        logger.warning(f"خطأ في الاستعلام عن الرمز {symbol}: HTTP {response.status}")

            # إذا لم يتم العثور على الرمز، حاول إضافة USDT
            if not symbol.endswith('USDT'):
                symbol_with_usdt = f"{symbol}USDT"
                logger.info(f"محاولة البحث عن الرمز مع USDT: {symbol_with_usdt}")
                params = {'symbol': symbol_with_usdt}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params, timeout=10) as response:
                        if response.status == 200:
                            data = await response.json()
                            symbols = data.get('symbols', [])
                            logger.info(f"تم الحصول على {len(symbols)} رمز من Binance للتحقق من {symbol_with_usdt}")

                            for symbol_info in symbols:
                                if symbol_info.get('symbol') == symbol_with_usdt and symbol_info.get('status') == 'TRADING':
                                    logger.info(f"✅ الرمز {symbol} متاح كـ {symbol_with_usdt} في Binance")
                                    return True
                        else:
                            logger.warning(f"خطأ في الاستعلام عن الرمز {symbol_with_usdt}: HTTP {response.status}")

            logger.warning(f"❌ الرمز {symbol} غير متاح للتداول في Binance")
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من توفر الرمز {symbol}: {str(e)}")
            return False

    async def get_klines(self, symbol: str, interval: str = '4h', limit: int = 100, user_id: str = None):
        """
        جلب بيانات الشموع مع الذاكرة المحلية

        Args:
            symbol: رمز العملة
            interval: الإطار الزمني (1h, 4h, 1d, 1w)
            limit: عدد الشموع المطلوبة
            user_id: معرف المستخدم (اختياري) - إذا تم توفيره، سيتم استخدام مفاتيح API الخاصة بالمستخدم
        """
        try:
            # التحقق من الذاكرة المحلية
            cache_key = f"klines_{symbol}_{interval}"
            current_time = datetime.now()

            if cache_key in self.market_data_cache and cache_key in self.market_data_expiry:
                if current_time < self.market_data_expiry[cache_key]:
                    return self.market_data_cache[cache_key]

            # التحقق من صحة الإطار الزمني
            valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
            if interval not in valid_intervals:
                logger.error(f"Invalid interval: {interval}. Using default 4h interval.")
                interval = '4h'

            # التحقق من توفر العملة
            if not await self.check_symbol_availability(symbol):
                logger.error(f"Symbol {symbol} is not available for trading")
                return None

            # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
            # ملاحظة: سيتم تمرير api_manager من الخارج عند الحاجة
            api_manager = getattr(self, 'api_manager', None)  # سيتم تعيينه من main.py

            if user_id and api_manager:
                try:
                    # الحصول على مفاتيح API الخاصة بالمستخدم
                    api_key, api_secret = await api_manager.get_api_keys(user_id, 'binance')

                    if api_key and api_secret:
                        logger.info(f"✅ استخدام مفاتيح API الخاصة بالمستخدم {user_id} للحصول على بيانات {symbol}")

                        # استخدام مكتبة python-binance مع مفاتيح API المستخدم
                        from binance.client import Client
                        client = Client(api_key, api_secret)

                        # تنظيف وتحويل الرمز للتأكد من التنسيق الصحيح
                        clean_symbol_for_api = symbol.upper().strip()
                        if not clean_symbol_for_api.endswith('USDT'):
                            clean_symbol_for_api = f"{clean_symbol_for_api}USDT"

                        logger.info(f"جاري الحصول على بيانات الشموع للرمز {symbol} -> {clean_symbol_for_api} بالإطار الزمني {interval}")

                        # الحصول على بيانات الشموع
                        klines = client.get_klines(
                            symbol=clean_symbol_for_api,
                            interval=interval,
                            limit=limit
                        )

                        if klines:
                            logger.info(f"✅ تم الحصول على {len(klines)} شمعة للرمز {symbol} باستخدام API المستخدم")
                            # تخزين في الذاكرة المحلية
                            self.market_data_cache[cache_key] = klines
                            self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)
                            return klines
                        else:
                            logger.warning(f"❌ لم يتم الحصول على بيانات شموع للرمز {symbol} باستخدام API المستخدم")
                    else:
                        logger.info(f"لا توجد مفاتيح API للمستخدم {user_id}")
                except Exception as user_api_error:
                    logger.error(f"❌ فشل في استخدام مفاتيح API الخاصة بالمستخدم {user_id}: {str(user_api_error)}")
                    # سنستمر باستخدام API العام

            # جلب البيانات من Binance باستخدام API العام
            # تنظيف وتحويل الرمز للتأكد من التنسيق الصحيح
            clean_symbol_for_public_api = symbol.upper().strip()
            if not clean_symbol_for_public_api.endswith('USDT'):
                clean_symbol_for_public_api = f"{clean_symbol_for_public_api}USDT"

            logger.info(f"استخدام API العام للرمز {symbol} -> {clean_symbol_for_public_api}")

            url = f"{self.base_url}/klines"
            params = {
                'symbol': clean_symbol_for_public_api,
                'interval': interval,
                'limit': limit
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        # تخزين في الذاكرة المحلية
                        self.market_data_cache[cache_key] = data
                        self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)
                        return data
                    else:
                        error_text = await response.text()
                        logger.error(f"Error fetching klines: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"Error getting klines for {symbol}: {str(e)}")
            return None

    async def get_klines_optimized(self, symbol: str, interval: str = '1h', limit: int = 100, user_id: str = None) -> Optional[List]:
        """الحصول على بيانات الشموع مع تحسين الأداء"""
        try:
            # استخدام محسن API
            from integrations.api_optimizer import api_optimizer, APIRequest

            # تنظيف الرمز
            clean_symbol = symbol.upper().strip()
            if not clean_symbol.endswith('USDT'):
                clean_symbol = f"{clean_symbol}USDT"

            # إنشاء طلب API محسن
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': clean_symbol,
                'interval': interval,
                'limit': limit
            }

            request = APIRequest(
                url=url,
                method='GET',
                params=params,
                timeout=10,
                priority=1  # أولوية عالية لبيانات السوق
            )

            # تنفيذ الطلب المحسن
            logger.info(f"🚀 استخدام محسن API للحصول على بيانات {clean_symbol}")
            result = await api_optimizer.execute_request(request)

            if result:
                logger.info(f"✅ تم الحصول على {len(result)} شمعة لـ {clean_symbol} بنجاح")
                return result
            else:
                logger.warning(f"⚠️ فشل في الحصول على بيانات {clean_symbol} باستخدام محسن API")
                # العودة للطريقة التقليدية
                return await self.get_klines(symbol, interval, limit, user_id)

        except Exception as e:
            logger.error(f"خطأ في الحصول على بيانات محسنة لـ {symbol}: {str(e)}")
            # العودة للطريقة التقليدية
            return await self.get_klines(symbol, interval, limit, user_id)

    def set_api_manager(self, api_manager):
        """تعيين مدير API للاستخدام في جلب البيانات"""
        self.api_manager = api_manager
