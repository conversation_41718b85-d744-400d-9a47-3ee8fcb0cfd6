"""
وحدة المؤشرات المحسنة - Optimized Technical Indicators
تحتوي على خوارزميات محسنة للمؤشرات الفنية مع تحسينات الأداء والدقة

الميزات الجديدة:
- خوارزميات محسنة للسرعة
- حساب متوازي للمؤشرات
- تخزين مؤقت ذكي
- تحسين استخدام الذاكرة
- دقة أعلى في الحسابات
"""

import numpy as np
import pandas as pd
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import Dict, List, Tuple, Optional, Union
from functools import lru_cache
import numba
from numba import jit, prange
from datetime import datetime, timedelta

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

# تم حذف TA-Lib نهائياً واستبداله بمؤشرات محلية محسنة
TALIB_AVAILABLE = False

# استيراد المؤشرات المحلية المحسنة
try:
    from .local_indicators import LocalTechnicalIndicators, local_indicators
    LOCAL_INDICATORS_AVAILABLE = True
    logger.info("تم تحميل المؤشرات المحلية المحسنة بنجاح")
except ImportError as e:
    LOCAL_INDICATORS_AVAILABLE = False
    logger.warning(f"فشل في تحميل المؤشرات المحلية: {e}")

class OptimizedIndicators:
    """كلاس المؤشرات المحسنة مع تحسينات الأداء المتقدمة"""
    
    def __init__(self, max_workers: int = 4):
        """
        تهيئة المؤشرات المحسنة
        
        Args:
            max_workers: عدد العمليات المتوازية القصوى
        """
        self.max_workers = max_workers
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=max_workers)
        self.cache = {}
        self.cache_timeout = 300  # 5 دقائق
        
    @staticmethod
    @jit(nopython=True, cache=True)
    def _fast_rsi_calculation(prices: np.ndarray, period: int = 14) -> np.ndarray:
        """
        حساب RSI محسن باستخدام Numba للسرعة القصوى
        
        Args:
            prices: مصفوفة الأسعار
            period: فترة الحساب
            
        Returns:
            مصفوفة قيم RSI
        """
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0.0)
        losses = np.where(deltas < 0, -deltas, 0.0)
        
        # حساب المتوسط المتحرك الأسي للمكاسب والخسائر
        avg_gains = np.zeros_like(gains)
        avg_losses = np.zeros_like(losses)
        
        # الحساب الأولي
        if len(gains) >= period:
            avg_gains[period-1] = np.mean(gains[:period])
            avg_losses[period-1] = np.mean(losses[:period])
            
            # الحساب التدريجي
            alpha = 1.0 / period
            for i in range(period, len(gains)):
                avg_gains[i] = alpha * gains[i] + (1 - alpha) * avg_gains[i-1]
                avg_losses[i] = alpha * losses[i] + (1 - alpha) * avg_losses[i-1]
        
        # حساب RSI
        rsi = np.zeros(len(prices))
        for i in range(period, len(prices)):
            if avg_losses[i-1] == 0:
                rsi[i] = 100.0
            else:
                rs = avg_gains[i-1] / avg_losses[i-1]
                rsi[i] = 100.0 - (100.0 / (1.0 + rs))
                
        return rsi
    
    @staticmethod
    @jit(nopython=True, cache=True)
    def _fast_ema_calculation(prices: np.ndarray, period: int) -> np.ndarray:
        """
        حساب EMA محسن باستخدام Numba
        
        Args:
            prices: مصفوفة الأسعار
            period: فترة الحساب
            
        Returns:
            مصفوفة قيم EMA
        """
        alpha = 2.0 / (period + 1.0)
        ema = np.zeros_like(prices)
        ema[0] = prices[0]
        
        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
            
        return ema
    
    @staticmethod
    @jit(nopython=True, cache=True)
    def _fast_macd_calculation(prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        حساب MACD محسن باستخدام Numba
        
        Args:
            prices: مصفوفة الأسعار
            fast: فترة EMA السريع
            slow: فترة EMA البطيء
            signal: فترة خط الإشارة
            
        Returns:
            tuple: (MACD, Signal Line, Histogram)
        """
        # حساب EMA السريع والبطيء
        fast_ema = OptimizedIndicators._fast_ema_calculation(prices, fast)
        slow_ema = OptimizedIndicators._fast_ema_calculation(prices, slow)
        
        # حساب MACD
        macd = fast_ema - slow_ema
        
        # حساب خط الإشارة
        signal_line = OptimizedIndicators._fast_ema_calculation(macd, signal)
        
        # حساب الهيستوجرام
        histogram = macd - signal_line
        
        return macd, signal_line, histogram
    
    @staticmethod
    @jit(nopython=True, cache=True)
    def _fast_bollinger_bands(prices: np.ndarray, period: int = 20, std_dev: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        حساب Bollinger Bands محسن
        
        Args:
            prices: مصفوفة الأسعار
            period: فترة الحساب
            std_dev: عدد الانحرافات المعيارية
            
        Returns:
            tuple: (Upper Band, Middle Band, Lower Band)
        """
        middle_band = np.zeros_like(prices)
        upper_band = np.zeros_like(prices)
        lower_band = np.zeros_like(prices)
        
        for i in range(period-1, len(prices)):
            window = prices[i-period+1:i+1]
            mean_val = np.mean(window)
            std_val = np.std(window)
            
            middle_band[i] = mean_val
            upper_band[i] = mean_val + (std_dev * std_val)
            lower_band[i] = mean_val - (std_dev * std_val)
            
        return upper_band, middle_band, lower_band
    
    @staticmethod
    @jit(nopython=True, cache=True)
    def _fast_stochastic_rsi(rsi: np.ndarray, period: int = 14) -> Tuple[np.ndarray, np.ndarray]:
        """
        حساب Stochastic RSI محسن
        
        Args:
            rsi: مصفوفة قيم RSI
            period: فترة الحساب
            
        Returns:
            tuple: (%K, %D)
        """
        stoch_rsi = np.zeros_like(rsi)
        
        for i in range(period-1, len(rsi)):
            window = rsi[i-period+1:i+1]
            min_rsi = np.min(window)
            max_rsi = np.max(window)
            
            if max_rsi - min_rsi != 0:
                stoch_rsi[i] = (rsi[i] - min_rsi) / (max_rsi - min_rsi) * 100
            else:
                stoch_rsi[i] = 50.0
        
        # حساب %K و %D (متوسطات متحركة)
        k_period = 3
        d_period = 3
        
        k_values = OptimizedIndicators._simple_moving_average(stoch_rsi, k_period)
        d_values = OptimizedIndicators._simple_moving_average(k_values, d_period)
        
        return k_values, d_values
    
    @staticmethod
    @jit(nopython=True, cache=True)
    def _simple_moving_average(prices: np.ndarray, period: int) -> np.ndarray:
        """
        حساب المتوسط المتحرك البسيط محسن
        
        Args:
            prices: مصفوفة الأسعار
            period: فترة الحساب
            
        Returns:
            مصفوفة قيم SMA
        """
        sma = np.zeros_like(prices)
        
        for i in range(period-1, len(prices)):
            sma[i] = np.mean(prices[i-period+1:i+1])
            
        return sma
    
    async def calculate_all_indicators_parallel(self, df: pd.DataFrame) -> Dict[str, Union[float, np.ndarray]]:
        """
        حساب جميع المؤشرات بشكل متوازي للحصول على أقصى سرعة
        
        Args:
            df: DataFrame يحتوي على بيانات السوق
            
        Returns:
            قاموس يحتوي على جميع المؤشرات المحسوبة
        """
        try:
            # تحويل البيانات إلى numpy arrays للسرعة
            close_prices = df['close'].values.astype(np.float64)
            high_prices = df['high'].values.astype(np.float64)
            low_prices = df['low'].values.astype(np.float64)
            volume = df['volume'].values.astype(np.float64)
            
            # إنشاء مهام متوازية لحساب المؤشرات
            tasks = []
            
            # المؤشرات الأساسية
            tasks.append(self._calculate_rsi_async(close_prices))
            tasks.append(self._calculate_ema_async(close_prices))
            tasks.append(self._calculate_macd_async(close_prices))
            tasks.append(self._calculate_bollinger_async(close_prices))
            tasks.append(self._calculate_stoch_rsi_async(close_prices))
            
            # المؤشرات المتقدمة
            tasks.append(self._calculate_adx_async(high_prices, low_prices, close_prices))
            tasks.append(self._calculate_ichimoku_async(high_prices, low_prices, close_prices))
            tasks.append(self._calculate_volume_indicators_async(close_prices, volume))
            
            # تشغيل جميع المهام بشكل متوازي
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # دمج النتائج
            indicators = {}
            for result in results:
                if isinstance(result, dict) and not isinstance(result, Exception):
                    indicators.update(result)
                elif isinstance(result, Exception):
                    logger.warning(f"خطأ في حساب مؤشر: {str(result)}")
            
            return indicators
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات المتوازية: {str(e)}")
            return {}
    
    async def _calculate_rsi_async(self, prices: np.ndarray) -> Dict[str, float]:
        """حساب RSI بشكل غير متزامن"""
        loop = asyncio.get_event_loop()

        def calculate():
            if LOCAL_INDICATORS_AVAILABLE:
                # استخدام المؤشرات المحلية المحسنة
                rsi_values = local_indicators.rsi(prices, 14)
            else:
                # استخدام الحساب المحلي السابق
                rsi_values = self._fast_rsi_calculation(prices, 14)

            return {
                'rsi': float(rsi_values[-1]) if len(rsi_values) > 0 else 50.0,
                'rsi_array': rsi_values
            }

        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_ema_async(self, prices: np.ndarray) -> Dict[str, float]:
        """حساب EMA بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            ema20 = self._fast_ema_calculation(prices, 20)
            ema50 = self._fast_ema_calculation(prices, 50)
            ema200 = self._fast_ema_calculation(prices, 200)
            
            return {
                'ema20': float(ema20[-1]) if len(ema20) > 0 else float(prices[-1]),
                'ema50': float(ema50[-1]) if len(ema50) > 0 else float(prices[-1]),
                'ema200': float(ema200[-1]) if len(ema200) > 0 else float(prices[-1]),
                'ema20_array': ema20,
                'ema50_array': ema50,
                'ema200_array': ema200
            }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_macd_async(self, prices: np.ndarray) -> Dict[str, float]:
        """حساب MACD بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            if LOCAL_INDICATORS_AVAILABLE:
                # استخدام المؤشرات المحلية المحسنة
                macd, signal, histogram = local_indicators.macd(prices)
            else:
                # استخدام الحساب المحلي السابق
                macd, signal, histogram = self._fast_macd_calculation(prices)

            return {
                'macd': float(macd[-1]) if len(macd) > 0 else 0.0,
                'macd_signal': float(signal[-1]) if len(signal) > 0 else 0.0,
                'macd_histogram': float(histogram[-1]) if len(histogram) > 0 else 0.0,
                'macd_array': macd,
                'macd_signal_array': signal,
                'macd_histogram_array': histogram
            }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_bollinger_async(self, prices: np.ndarray) -> Dict[str, float]:
        """حساب Bollinger Bands بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            upper, middle, lower = self._fast_bollinger_bands(prices)
            
            return {
                'bb_upper': float(upper[-1]) if len(upper) > 0 else float(prices[-1]),
                'bb_middle': float(middle[-1]) if len(middle) > 0 else float(prices[-1]),
                'bb_lower': float(lower[-1]) if len(lower) > 0 else float(prices[-1]),
                'bb_upper_array': upper,
                'bb_middle_array': middle,
                'bb_lower_array': lower
            }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_stoch_rsi_async(self, prices: np.ndarray) -> Dict[str, float]:
        """ح��اب Stochastic RSI بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            rsi_values = self._fast_rsi_calculation(prices, 14)
            k_values, d_values = self._fast_stochastic_rsi(rsi_values)
            
            return {
                'stoch_k': float(k_values[-1]) if len(k_values) > 0 else 50.0,
                'stoch_d': float(d_values[-1]) if len(d_values) > 0 else 50.0,
                'stoch_k_array': k_values,
                'stoch_d_array': d_values
            }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_adx_async(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, float]:
        """حساب ADX بشكل غير متزامن"""
        loop = asyncio.get_event_loop()

        def calculate():
            try:
                # استخدام الحساب المحلي المحسن بدلاً من TA-Lib
                adx_values, plus_di, minus_di = self._calculate_adx_manual(high, low, close)

                return {
                    'adx': float(adx_values[-1]) if len(adx_values) > 0 else 25.0,
                    'plus_di': float(plus_di[-1]) if len(plus_di) > 0 else 25.0,
                    'minus_di': float(minus_di[-1]) if len(minus_di) > 0 else 25.0,
                    'adx_array': adx_values,
                    'plus_di_array': plus_di,
                    'minus_di_array': minus_di
                }
            except Exception as e:
                logger.warning(f"فشل في حساب ADX: {str(e)}")
                return {
                    'adx': 25.0,
                    'plus_di': 25.0,
                    'minus_di': 25.0
                }

        return await loop.run_in_executor(self.thread_executor, calculate)

    @staticmethod
    @jit(nopython=True, cache=True)
    def _calculate_adx_manual(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        حساب ADX يدوياً بدون TA-Lib

        Args:
            high: مصفوفة الأسعار العالية
            low: مصفوفة الأسعار المنخفضة
            close: مصفوفة أسعار الإغلاق
            period: فترة الحساب

        Returns:
            tuple: (ADX, +DI, -DI)
        """
        n = len(close)

        # حساب True Range
        tr = np.zeros(n)
        plus_dm = np.zeros(n)
        minus_dm = np.zeros(n)

        for i in range(1, n):
            # True Range
            hl = high[i] - low[i]
            hc = abs(high[i] - close[i-1])
            lc = abs(low[i] - close[i-1])
            tr[i] = max(hl, hc, lc)

            # Directional Movement
            up_move = high[i] - high[i-1]
            down_move = low[i-1] - low[i]

            if up_move > down_move and up_move > 0:
                plus_dm[i] = up_move
            else:
                plus_dm[i] = 0

            if down_move > up_move and down_move > 0:
                minus_dm[i] = down_move
            else:
                minus_dm[i] = 0

        # حساب المتوسطات المتحركة
        atr = np.zeros(n)
        plus_di = np.zeros(n)
        minus_di = np.zeros(n)
        adx = np.zeros(n)

        # الحساب الأولي
        if n >= period:
            atr[period] = np.mean(tr[1:period+1])
            plus_di_sum = np.mean(plus_dm[1:period+1])
            minus_di_sum = np.mean(minus_dm[1:period+1])

            plus_di[period] = 100 * plus_di_sum / atr[period] if atr[period] != 0 else 0
            minus_di[period] = 100 * minus_di_sum / atr[period] if atr[period] != 0 else 0

            # حساب باقي القيم
            for i in range(period + 1, n):
                atr[i] = (atr[i-1] * (period - 1) + tr[i]) / period
                plus_di_smooth = (plus_di[i-1] * (period - 1) + plus_dm[i]) / period
                minus_di_smooth = (minus_di[i-1] * (period - 1) + minus_dm[i]) / period

                plus_di[i] = 100 * plus_di_smooth / atr[i] if atr[i] != 0 else 0
                minus_di[i] = 100 * minus_di_smooth / atr[i] if atr[i] != 0 else 0

                # حساب ADX
                dx = abs(plus_di[i] - minus_di[i]) / (plus_di[i] + minus_di[i]) * 100 if (plus_di[i] + minus_di[i]) != 0 else 0

                if i == period + 1:
                    adx[i] = dx
                else:
                    adx[i] = (adx[i-1] * (period - 1) + dx) / period

        return adx, plus_di, minus_di

    async def _calculate_ichimoku_async(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, float]:
        """حساب Ichimoku Cloud بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            try:
                # حساب مؤشرات Ichimoku
                conversion_period = 9
                base_period = 26
                span_period = 52
                displacement = 26
                
                # Tenkan-sen (Conversion Line)
                tenkan_high = pd.Series(high).rolling(window=conversion_period).max()
                tenkan_low = pd.Series(low).rolling(window=conversion_period).min()
                tenkan_sen = (tenkan_high + tenkan_low) / 2
                
                # Kijun-sen (Base Line)
                kijun_high = pd.Series(high).rolling(window=base_period).max()
                kijun_low = pd.Series(low).rolling(window=base_period).min()
                kijun_sen = (kijun_high + kijun_low) / 2
                
                # Senkou Span A (Leading Span A)
                senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)
                
                # Senkou Span B (Leading Span B)
                span_high = pd.Series(high).rolling(window=span_period).max()
                span_low = pd.Series(low).rolling(window=span_period).min()
                senkou_span_b = ((span_high + span_low) / 2).shift(displacement)
                
                # Chikou Span (Lagging Span)
                chikou_span = pd.Series(close).shift(-displacement)
                
                return {
                    'ichimoku_tenkan': float(tenkan_sen.iloc[-1]) if not pd.isna(tenkan_sen.iloc[-1]) else float(close[-1]),
                    'ichimoku_kijun': float(kijun_sen.iloc[-1]) if not pd.isna(kijun_sen.iloc[-1]) else float(close[-1]),
                    'ichimoku_senkou_a': float(senkou_span_a.iloc[-displacement]) if len(senkou_span_a) > displacement and not pd.isna(senkou_span_a.iloc[-displacement]) else float(close[-1]),
                    'ichimoku_senkou_b': float(senkou_span_b.iloc[-displacement]) if len(senkou_span_b) > displacement and not pd.isna(senkou_span_b.iloc[-displacement]) else float(close[-1]),
                    'ichimoku_chikou': float(chikou_span.iloc[-1]) if not pd.isna(chikou_span.iloc[-1]) else float(close[-1])
                }
            except Exception as e:
                logger.warning(f"فشل في حساب Ichimoku: {str(e)}")
                return {
                    'ichimoku_tenkan': float(close[-1]),
                    'ichimoku_kijun': float(close[-1]),
                    'ichimoku_senkou_a': float(close[-1]),
                    'ichimoku_senkou_b': float(close[-1]),
                    'ichimoku_chikou': float(close[-1])
                }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    async def _calculate_volume_indicators_async(self, close: np.ndarray, volume: np.ndarray) -> Dict[str, float]:
        """حساب مؤشرات الحجم بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        
        def calculate():
            try:
                # Volume Weighted Average Price (VWAP)
                typical_price = close  # تبسيط للحساب السريع
                vwap = np.cumsum(typical_price * volume) / np.cumsum(volume)
                
                # On Balance Volume (OBV)
                obv = np.zeros_like(close)
                for i in range(1, len(close)):
                    if close[i] > close[i-1]:
                        obv[i] = obv[i-1] + volume[i]
                    elif close[i] < close[i-1]:
                        obv[i] = obv[i-1] - volume[i]
                    else:
                        obv[i] = obv[i-1]
                
                # Volume Moving Average
                volume_ma = self._simple_moving_average(volume, 20)
                
                return {
                    'vwap': float(vwap[-1]) if len(vwap) > 0 else float(close[-1]),
                    'obv': float(obv[-1]) if len(obv) > 0 else 0.0,
                    'volume_ma': float(volume_ma[-1]) if len(volume_ma) > 0 else float(volume[-1]),
                    'volume_ratio': float(volume[-1] / volume_ma[-1]) if len(volume_ma) > 0 and volume_ma[-1] != 0 else 1.0
                }
            except Exception as e:
                logger.warning(f"فشل في حساب مؤشرات الحجم: {str(e)}")
                return {
                    'vwap': float(close[-1]),
                    'obv': 0.0,
                    'volume_ma': float(volume[-1]),
                    'volume_ratio': 1.0
                }
        
        return await loop.run_in_executor(self.thread_executor, calculate)
    
    def get_cache_key(self, symbol: str, timeframe: str, indicators: List[str]) -> str:
        """إنشاء مفتاح التخزين المؤقت"""
        return f"{symbol}_{timeframe}_{'_'.join(sorted(indicators))}"
    
    def is_cache_valid(self, cache_key: str) -> bool:
        """التحقق من صحة التخزين المؤقت"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def cleanup_cache(self):
        """تنظيف التخزين المؤقت المنتهي الصلاحية"""
        current_time = datetime.now().timestamp()
        expired_keys = [
            key for key, value in self.cache.items()
            if (current_time - value.get('timestamp', 0)) > self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        logger.info(f"تم حذف {len(expired_keys)} عنصر منتهي الصلاحية من التخزين المؤقت")
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        try:
            self.thread_executor.shutdown(wait=False)
            self.process_executor.shutdown(wait=False)
        except:
            pass


# إنشاء مثيل عام للاستخدام
optimized_indicators = OptimizedIndicators()