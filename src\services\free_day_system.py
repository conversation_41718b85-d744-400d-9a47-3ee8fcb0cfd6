"""
نظام إدارة اليوم المجاني الأسبوعي
يتيح للمستخدمين تجربة الميزات المدفوعة مجانًا ليوم واحد في الأسبوع
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from firebase_admin import firestore
import pytz

# استيراد وحدة تهيئة Firebase
from integrations.firebase_init import initialize_firebase

# إعداد التسجيل
logger = logging.getLogger(__name__)

class FreeDaySystem:
    """نظام إدارة اليوم المجاني الأسبوعي"""

    def __init__(self, db=None):
        """
        تهيئة نظام اليوم المجاني

        Args:
            db: قاعدة بيانات Firestore
        """
        # محاولة الحصول على قاعدة البيانات من الوسيط أو تهيئة Firebase
        if db is None:
            logger.info("جاري تهيئة Firebase من وحدة firebase_init...")
            db = initialize_firebase()
            if db is None:
                logger.error("فشل في تهيئة Firebase، سيتم محاولة استخدام firestore.client() مباشرة")
                try:
                    db = firestore.client()
                except Exception as e:
                    logger.error(f"فشل في الاتصال بـ Firestore: {str(e)}")
                    raise ValueError("لم يتم تهيئة قاعدة البيانات بعد")

        self.db = db
        self._cache = {}  # ذاكرة مؤقتة لبيانات اليوم المجاني
        self._cache_expiry = {}  # وقت انتهاء صلاحية الذاكرة المؤقتة

    def get_user_free_day_status(self, user_id: str) -> Dict[str, Any]:
        """
        الحصول على حالة اليوم المجاني للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            قاموس يحتوي على حالة اليوم المجاني
        """
        try:
            # التحقق من الذاكرة المؤقتة
            current_time = datetime.now()
            if user_id in self._cache and user_id in self._cache_expiry:
                if current_time < self._cache_expiry[user_id]:
                    return self._cache[user_id]

            # الحصول على بيانات المستخدم من Firestore
            user_ref = self.db.collection('users').document(user_id)
            user_data = user_ref.get()

            if not user_data.exists:
                logger.warning(f"المستخدم {user_id} غير موجود في قاعدة البيانات")
                # محاولة الحصول على اللغة من user_settings حتى لو لم يوجد المستخدم في users
                try:
                    user_settings_ref = self.db.collection('user_settings').document(user_id)
                    user_settings_doc = user_settings_ref.get()
                    default_lang = 'ar'
                    if user_settings_doc.exists:
                        settings = user_settings_doc.to_dict()
                        default_lang = settings.get('lang', 'ar')
                    return self._get_default_free_day_status(default_lang)
                except Exception:
                    return self._get_default_free_day_status()

            user_dict = user_data.to_dict()

            # التحقق من وجود بيانات اليوم المجاني
            if 'free_day_of_week' not in user_dict:
                # تعيين يوم افتراضي (الاثنين)
                free_day = 0  # الاثنين (0-6 حيث 0 هو الاثنين)
                last_used = None
                is_active = False

                # تحديث بيانات المستخدم
                user_ref.update({
                    'free_day_of_week': free_day,
                    'last_free_day_used': None,
                    'is_free_day_active': False
                })
            else:
                free_day = user_dict.get('free_day_of_week', 0)
                last_used = user_dict.get('last_free_day_used')
                is_active = user_dict.get('is_free_day_active', False)

                if last_used:
                    last_used = datetime.fromisoformat(last_used)

            # الحصول على لغة المستخدم - تحسين دقة الكشف
            lang = 'ar'  # القيمة الافتراضية

            try:
                # محاولة الحصول على اللغة من user_settings أولاً
                user_settings_ref = self.db.collection('user_settings').document(user_id)
                user_settings_doc = user_settings_ref.get()

                if user_settings_doc.exists:
                    settings = user_settings_doc.to_dict()
                    lang = settings.get('lang', 'ar')
                    logger.debug(f"تم العثور على لغة المستخدم {user_id} في user_settings: {lang}")
                else:
                    # إذا لم توجد في user_settings، تحقق من users collection
                    if 'lang' in user_dict:
                        lang = user_dict.get('lang', 'ar')
                        logger.debug(f"تم العثور على لغة المستخدم {user_id} في users: {lang}")
                    else:
                        logger.debug(f"لم يتم العثور على لغة محددة للمستخدم {user_id}، استخدام الافتراضية: ar")

            except Exception as e:
                logger.warning(f"خطأ في الحصول على لغة المستخدم {user_id}: {str(e)}")
                lang = 'ar'

            # إنشاء قاموس الحالة
            status = {
                'free_day_of_week': free_day,
                'day_name': self._get_day_name(free_day, lang),
                'last_free_day_used': last_used,
                'is_free_day_active': is_active,
                'next_free_day': self._calculate_next_free_day(free_day, last_used),
                'lang': lang
            }

            # تخزين في الذاكرة المؤقتة
            self._cache[user_id] = status
            self._cache_expiry[user_id] = current_time + timedelta(hours=1)

            return status

        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return self._get_default_free_day_status()

    def _get_default_free_day_status(self, lang: str = 'ar') -> Dict[str, Any]:
        """
        الحصول على حالة افتراضية لليوم المجاني

        Args:
            lang: اللغة المستخدمة ('ar' للعربية، 'en' للإنجليزية)

        Returns:
            قاموس يحتوي على حالة افتراضية لليوم المجاني
        """
        free_day = 0  # الاثنين
        return {
            'free_day_of_week': free_day,
            'day_name': self._get_day_name(free_day, lang),
            'last_free_day_used': None,
            'is_free_day_active': False,
            'next_free_day': self._calculate_next_free_day(free_day),
            'lang': lang
        }

    def _get_day_name(self, day_index: int, lang: str = 'ar') -> str:
        """
        الحصول على اسم اليوم بالعربية أو الإنجليزية حسب اللغة المحددة

        Args:
            day_index: مؤشر اليوم (0-6 حيث 0 هو الاثنين)
            lang: اللغة المستخدمة ('ar' للعربية، 'en' للإنجليزية)

        Returns:
            اسم اليوم باللغة المحددة
        """
        days_ar = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        days_en = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        if lang == 'en':
            return days_en[day_index % 7]
        else:
            return days_ar[day_index % 7]

    def _calculate_next_free_day(self, free_day: int, last_used: Optional[datetime] = None) -> datetime:
        """
        حساب تاريخ اليوم المجاني القادم

        Args:
            free_day: مؤشر اليوم المجاني (0-6 حيث 0 هو الاثنين)
            last_used: تاريخ آخر استخدام لليوم المجاني

        Returns:
            تاريخ اليوم المجاني القادم
        """
        today = datetime.now().date()
        current_weekday = today.weekday()

        # حساب اليوم المجاني القادم بشكل افتراضي
        if current_weekday < free_day:
            days_until_free_day = free_day - current_weekday
        else:
            days_until_free_day = 7 - (current_weekday - free_day)

        next_free_day = today + timedelta(days=days_until_free_day)

        # إذا كان المستخدم قد استخدم اليوم المجاني هذا الأسبوع، يجب أن يكون اليوم المجاني القادم في الأسبوع القادم فقط
        if last_used:
            last_used_date = last_used.date()
            # إذا كان last_used في نفس أسبوع اليوم الحالي
            if last_used_date.isocalendar()[1] == today.isocalendar()[1]:
                # اليوم المجاني القادم يجب أن يكون بعد 7 أيام على الأقل من آخر استخدام
                if next_free_day <= today:
                    next_free_day = today + timedelta(days=7 - (current_weekday - free_day))
                else:
                    next_free_day = next_free_day + timedelta(days=7)
        return datetime.combine(next_free_day, datetime.min.time())

    def is_today_free_day(self, user_id: str) -> bool:
        """
        التحقق مما إذا كان اليوم الحالي هو اليوم المجاني للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            True إذا كان اليوم الحالي هو اليوم المجاني، False خلاف ذلك
        """
        try:
            status = self.get_user_free_day_status(user_id)
            free_day = status['free_day_of_week']
            today = datetime.now().date()

            # التحقق من اليوم الحالي
            if today.weekday() != free_day:
                return False

            # التحقق من آخر استخدام
            last_used = status['last_free_day_used']
            if last_used:
                # التحقق من أن آخر استخدام ليس في هذا الأسبوع
                if last_used.date().isocalendar()[1] == today.isocalendar()[1]:
                    return False

            return True

        except Exception as e:
            logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def activate_free_day(self, user_id: str) -> bool:
        """
        تفعيل اليوم المجاني للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            True إذا تم التفعيل بنجاح، False خلاف ذلك
        """
        try:
            # التحقق من أهلية المستخدم لليوم المجاني
            if not self.is_today_free_day(user_id):
                logger.warning(f"المستخدم {user_id} غير مؤهل لليوم المجاني اليوم")
                return False

            # تحديث حالة اليوم المجاني
            user_ref = self.db.collection('users').document(user_id)
            current_time = datetime.now()

            # تحديث كلا النظامين لضمان التوافق
            user_ref.update({
                'is_free_day_active': True,  # النظام القديم
                'free_day_active': True,     # النظام الجديد
                'free_day_start': current_time.isoformat(),
                'free_day_end': (current_time + timedelta(hours=24)).isoformat(),
                'last_free_day_used': current_time.isoformat()
            })

            # تحديث الذاكرة المؤقتة
            if user_id in self._cache:
                self._cache[user_id]['is_free_day_active'] = True
                self._cache[user_id]['last_free_day_used'] = current_time

            # إرسال إشعار للمستخدم بتفعيل اليوم المجاني
            await self.send_free_day_notification(user_id, 'available')

            logger.info(f"تم تفعيل اليوم المجاني للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def deactivate_free_day(self, user_id: str) -> bool:
        """
        إلغاء تفعيل اليوم المجاني للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            True إذا تم إلغاء التفعيل بنجاح، False خلاف ذلك
        """
        try:
            # تحديث حالة اليوم المجاني
            user_ref = self.db.collection('users').document(user_id)

            # إلغاء تفعيل كلا النظامين
            user_ref.update({
                'is_free_day_active': False,  # النظام القديم
                'free_day_active': False      # النظام الجديد
            })

            # تحديث الذاكرة المؤقتة
            if user_id in self._cache:
                self._cache[user_id]['is_free_day_active'] = False

            logger.info(f"تم إلغاء تفعيل اليوم المجاني للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في إلغاء تفعيل اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def set_free_day(self, user_id: str, day_index: int) -> bool:
        """
        تعيين اليوم المجاني للمستخدم

        Args:
            user_id: معرف المستخدم
            day_index: مؤشر اليوم (0-6 حيث 0 هو الاثنين)

        Returns:
            True إذا تم التعيين بنجاح، False خلاف ذلك
        """
        try:
            # التحقق من صحة مؤشر اليوم
            if day_index < 0 or day_index > 6:
                logger.warning(f"مؤشر اليوم {day_index} غير صالح")
                return False

            # تحديث اليوم المجاني
            user_ref = self.db.collection('users').document(user_id)

            user_ref.update({
                'free_day_of_week': day_index,
                'is_free_day_active': False  # إعادة تعيين حالة التفعيل
            })

            # تحديث الذاكرة المؤقتة
            if user_id in self._cache:
                self._cache[user_id]['free_day_of_week'] = day_index
                self._cache[user_id]['day_name'] = self._get_day_name(day_index)
                self._cache[user_id]['is_free_day_active'] = False
                self._cache[user_id]['next_free_day'] = self._calculate_next_free_day(day_index, self._cache[user_id]['last_free_day_used'])

            logger.info(f"تم تعيين اليوم المجاني للمستخدم {user_id} إلى {self._get_day_name(day_index)}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تعيين اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def cleanup_expired_free_days(self) -> int:
        """
        تنظيف الأيام المجانية المنتهية الصلاحية

        Returns:
            عدد المستخدمين الذين تم تنظيف أيامهم المجانية المنتهية
        """
        try:
            cleaned_count = 0
            current_time = datetime.now(pytz.UTC)

            # الحصول على جميع المستخدمين
            users_ref = self.db.collection('users')
            users = users_ref.get()

            for user in users:
                try:
                    user_id = user.id
                    user_data = user.to_dict()

                    # تجاهل وثائق البيانات الوصفية
                    if user_id.startswith('_'):
                        continue

                    # التحقق من الأيام المجانية المؤقتة المنتهية
                    if user_data.get('free_day_active', False):
                        end_time_str = user_data.get('free_day_end')
                        if end_time_str:
                            try:
                                # تحويل الوقت مع معالجة المناطق الزمنية
                                if end_time_str.endswith('+00:00') or 'T' in end_time_str:
                                    end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                                    if end_time.tzinfo is None:
                                        end_time = end_time.replace(tzinfo=pytz.UTC)
                                else:
                                    end_time = datetime.fromisoformat(end_time_str)
                                    end_time = end_time.replace(tzinfo=pytz.UTC)

                                if current_time >= end_time:
                                    # انتهت الصلاحية، إلغاء التفعيل
                                    user.reference.update({
                                        'free_day_active': False
                                    })
                                    await self.send_free_day_notification(user_id, 'ended')
                                    cleaned_count += 1
                                    logger.info(f"تم تنظيف اليوم المجاني المنتهي للمستخدم {user_id}")
                            except Exception as e:
                                logger.warning(f"خطأ في تحليل وقت انتهاء اليوم المجاني للمستخدم {user_id}: {str(e)}")

                except Exception as e:
                    logger.error(f"خطأ في تنظيف اليوم المجاني للمستخدم {user.id}: {str(e)}")
                    continue

            logger.info(f"تم تنظيف {cleaned_count} يوم مجاني منتهي الصلاحية")
            return cleaned_count

        except Exception as e:
            logger.error(f"خطأ في تنظيف الأيام المجانية المنتهية: {str(e)}")
            return 0

    async def check_and_update_free_days(self) -> Tuple[int, int]:
        """
        التحقق من وتحديث حالة الأيام المجانية لجميع المستخدمين

        Returns:
            عدد المستخدمين الذين تم تفعيل اليوم المجاني لهم، وعدد المستخدمين الذين تم إلغاء تفعيل اليوم المجاني لهم
        """
        try:
            activated_count = 0
            deactivated_count = 0

            # الحصول على جميع المستخدمين
            users_ref = self.db.collection('users')
            users = users_ref.get()

            today = datetime.now().date()
            today_weekday = today.weekday()

            for user in users:
                try:
                    user_id = user.id
                    user_data = user.to_dict()

                    # تجاهل وثائق البيانات الوصفية
                    if user_id.startswith('_'):
                        continue

                    # التحقق من وجود بيانات اليوم المجاني
                    if 'free_day_of_week' not in user_data:
                        # تعيين يوم افتراضي (الاثنين)
                        user.reference.update({
                            'free_day_of_week': 0,
                            'last_free_day_used': None,
                            'is_free_day_active': False
                        })
                        continue

                    free_day = user_data.get('free_day_of_week', 0)
                    is_active = user_data.get('is_free_day_active', False)
                    last_used_str = user_data.get('last_free_day_used')

                    # التحقق من اليوم الحالي
                    if today_weekday == free_day:
                        # اليوم هو اليوم المجاني للمستخدم

                        # التحقق من آخر استخدام
                        if last_used_str:
                            last_used = datetime.fromisoformat(last_used_str).date()

                            # التحقق من أن آخر استخدام ليس في هذا الأسبوع
                            if last_used.isocalendar()[1] != today.isocalendar()[1]:
                                # لم يتم استخدام اليوم المجاني هذا الأسبوع
                                if not is_active:
                                    # تفعيل اليوم المجاني - تحديث كلا النظامين
                                    current_time = datetime.now()
                                    user.reference.update({
                                        'is_free_day_active': True,  # النظام القديم
                                        'free_day_active': True,     # النظام الجديد
                                        'free_day_start': current_time.isoformat(),
                                        'free_day_end': (current_time + timedelta(hours=24)).isoformat(),
                                        'last_free_day_used': current_time.isoformat()
                                    })
                                    await self.send_free_day_notification(user_id, 'available')
                                    activated_count += 1
                        else:
                            # لم يتم استخدام اليوم المجاني من قبل
                            if not is_active:
                                # تفعيل اليوم المجاني - تحديث كلا النظامين
                                current_time = datetime.now()
                                user.reference.update({
                                    'is_free_day_active': True,  # النظام القديم
                                    'free_day_active': True,     # النظام الجديد
                                    'free_day_start': current_time.isoformat(),
                                    'free_day_end': (current_time + timedelta(hours=24)).isoformat(),
                                    'last_free_day_used': current_time.isoformat()
                                })
                                await self.send_free_day_notification(user_id, 'available')
                                activated_count += 1
                    else:
                        # اليوم ليس اليوم المجاني للمستخدم
                        if is_active:
                            # إلغاء تفعيل اليوم المجاني
                            await self.deactivate_free_day(user_id)
                            await self.send_free_day_notification(user_id, 'ended')
                            deactivated_count += 1

                except Exception as e:
                    logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user.id}: {str(e)}")
                    continue

            logger.info(f"تم تفعيل اليوم المجاني لـ {activated_count} مستخدم وإلغاء تفعيل اليوم المجاني لـ {deactivated_count} مستخدم")
            return activated_count, deactivated_count

        except Exception as e:
            logger.error(f"خطأ في التحقق من وتحديث حالة الأيام المجانية: {str(e)}")
            return 0, 0

    async def send_free_day_notification(self, user_id: str, notification_type: str) -> bool:
        """
        إرسال إشعار متعلق باليوم المجاني

        Args:
            user_id: معرف المستخدم
            notification_type: نوع الإشعار (available, reminder, ended)

        Returns:
            True إذا تم إرسال الإشعار بنجاح، False خلاف ذلك
        """
        try:
            # الحصول على لغة المستخدم مباشرة من إعدادات المستخدم لضمان الدقة
            lang = 'ar'  # القيمة الافتراضية

            try:
                # محاولة الحصول على اللغة من user_settings
                user_settings_ref = self.db.collection('user_settings').document(user_id)
                user_settings_doc = user_settings_ref.get()

                if user_settings_doc.exists:
                    settings = user_settings_doc.to_dict()
                    lang = settings.get('lang', 'ar')
                    logger.debug(f"لغة المستخدم {user_id} للإشعار: {lang}")
                else:
                    # إذا لم توجد في user_settings، تحقق من users collection
                    user_ref = self.db.collection('users').document(user_id)
                    user_doc = user_ref.get()
                    if user_doc.exists:
                        user_data = user_doc.to_dict()
                        lang = user_data.get('lang', 'ar')

            except Exception as e:
                logger.warning(f"خطأ في الحصول على لغة المستخدم {user_id} للإشعار: {str(e)}")
                lang = 'ar'

            # الحصول على حالة اليوم المجاني
            status = self.get_user_free_day_status(user_id)
            day_name = status['day_name']
            next_free_day = status['next_free_day']

            # إنشاء نص الإشعار باستخدام نظام الترجمة
            try:
                from utils.text_helpers import get_text
            except ImportError:
                # في حالة عدم توفر نظام الترجمة، استخدام النصوص المباشرة
                get_text = None

            if notification_type == 'available':
                # اليوم المجاني متاح - التحقق من نوع التفعيل
                status = self.get_user_free_day_status(user_id)

                # التحقق من نوع التفعيل (أسبوعي أم مؤقت)
                is_weekly_free_day = self.is_today_free_day(user_id)

                if is_weekly_free_day:
                    # اليوم المجاني الأسبوعي
                    current_day_name = self._get_day_name(datetime.now().weekday(), lang)
                    if get_text:
                        message = get_text('free_day_available', lang, day_name=current_day_name)
                    else:
                        # النصوص الاحتياطية
                        if lang == 'ar':
                            message = f"🎁 *اليوم المجاني الأسبوعي متاح الآن!*\n\nيمكنك الاستمتاع بجميع الميزات المدفوعة مجانًا اليوم ({current_day_name}).\n\nاستفد من هذه الفرصة لتجربة جميع الميزات المتقدمة!"
                        else:
                            message = f"🎁 *Weekly Free Day Available Now!*\n\nYou can enjoy all premium features for free today ({current_day_name}).\n\nTake advantage of this opportunity to try all advanced features!"
                else:
                    # يوم مجاني مؤقت (ممنوح من الإدارة)
                    if get_text:
                        message = get_text('free_day_granted', lang)
                    else:
                        # النصوص الاحتياطية
                        if lang == 'ar':
                            message = f"🎁 *تم منحك يوم مجاني!*\n\nيمكنك الآن الاستمتاع بجميع الميزات المدفوعة مجانًا لمدة 24 ساعة.\n\nاستفد من هذه الفرصة لتجربة جميع الميزات المتقدمة!"
                        else:
                            message = f"🎁 *You've Been Granted a Free Day!*\n\nYou can now enjoy all premium features for free for 24 hours.\n\nTake advantage of this opportunity to try all advanced features!"

            elif notification_type == 'reminder':
                # تذكير باليوم المجاني القادم
                days_until = (next_free_day.date() - datetime.now().date()).days

                if get_text:
                    message = get_text('free_day_reminder', lang, day_name=day_name, days_until=days_until)
                else:
                    # النصوص الاحتياطية
                    if lang == 'ar':
                        message = f"🔔 *تذكير باليوم المجاني*\n\nيوم {day_name} القادم هو يومك المجاني الأسبوعي.\n\nستتمكن من الاستمتاع بجميع الميزات المدفوعة مجانًا خلال {days_until} يوم."
                    else:
                        message = f"🔔 *Free Day Reminder*\n\nNext {day_name} is your weekly free day.\n\nYou will be able to enjoy all premium features for free in {days_until} days."

            elif notification_type == 'ended':
                # انتهاء اليوم المجاني - التحقق من نوع الانتهاء
                status = self.get_user_free_day_status(user_id)

                # التحقق من نوع الانتهاء (أسبوعي أم مؤقت)
                user_ref = self.db.collection('users').document(user_id)
                user_doc = user_ref.get()

                if user_doc.exists:
                    user_data = user_doc.to_dict()
                    has_weekly_system = 'free_day_of_week' in user_data
                    has_temp_system = user_data.get('free_day_end') is not None

                    if has_weekly_system and not has_temp_system:
                        # نظام أسبوعي
                        if get_text:
                            message = get_text('free_day_ended', lang, day_name=day_name)
                        else:
                            # النصوص الاحتياطية
                            if lang == 'ar':
                                message = f"⏰ *انتهى اليوم المجاني الأسبوعي*\n\nانتهى يومك المجاني لهذا الأسبوع.\n\nيمكنك الاستمتاع بالميزات المدفوعة مرة أخرى يوم {day_name} القادم، أو الاشتراك للحصول على جميع الميزات بشكل دائم."
                            else:
                                message = f"⏰ *Weekly Free Day Ended*\n\nYour weekly free day has ended.\n\nYou can enjoy premium features again next {day_name}, or subscribe to get all features permanently."
                    else:
                        # نظام مؤقت
                        if get_text:
                            message = get_text('temp_free_day_ended', lang)
                        else:
                            # النصوص الاحتياطية
                            if lang == 'ar':
                                message = f"⏰ *انتهى اليوم المجاني المؤقت*\n\nانتهت فترة اليوم المجاني الممنوح لك.\n\nيمكنك الاشتراك للحصول على جميع الميزات بشكل دائم، أو انتظار يومك المجاني الأسبوعي يوم {day_name}."
                            else:
                                message = f"⏰ *Temporary Free Day Ended*\n\nYour granted free day period has ended.\n\nYou can subscribe to get all features permanently, or wait for your weekly free day on {day_name}."
                else:
                    # حالة افتراضية
                    if lang == 'ar':
                        message = "⏰ *انتهى اليوم المجاني*\n\nانتهت فترة اليوم المجاني.\n\nيمكنك الاشتراك للحصول على جميع الميزات بشكل دائم."
                    else:
                        message = "⏰ *Free Day Ended*\n\nYour free day period has ended.\n\nYou can subscribe to get all features permanently."

            else:
                logger.warning(f"نوع الإشعار {notification_type} غير معروف")
                return False

            # إرسال الإشعار - إصلاح مشكلة اللغة
            # التأكد من أن اللغة محددة بشكل صحيح
            if lang not in ['ar', 'en']:
                lang = 'ar'  # القيمة الافتراضية

            # تسجيل معلومات التصحيح
            logger.info(f"إرسال إشعار اليوم المجاني للمستخدم {user_id} باللغة: {lang}")

            # إضافة الإشعار إلى قاعدة البيانات
            notification_data = {
                'user_id': user_id,
                'type': f'free_day_{notification_type}',
                'text': message,  # استخدام 'text' بدلاً من 'message' للتوافق مع نظام إرسال الإشعارات
                'created_at': datetime.now().isoformat(),
                'read': False,
                'sent': False
            }

            notification_ref = self.db.collection('notifications').add(notification_data)
            logger.info(f"تم إضافة إشعار اليوم المجاني للمستخدم {user_id}")

            # محاولة إرسال الإشعار مباشرة عبر البوت إذا كان متاحاً
            try:
                # استيراد البوت من الملف الرئيسي
                from telegram import Bot
                import os
                from services.system_settings import system_settings

                # الحصول على رمز البوت
                bot_token = system_settings.get("BOT_TOKEN", None, sensitive=True)
                if not bot_token:
                    bot_token = os.getenv("BOT_TOKEN")

                if bot_token:
                    bot = Bot(token=bot_token)
                    await bot.send_message(chat_id=user_id, text=message)

                    # تحديث حالة الإشعار
                    notification_ref[1].update({'sent': True, 'sent_at': datetime.now().isoformat()})
                    logger.info(f"تم إرسال إشعار اليوم المجاني للمستخدم {user_id} مباشرة")
                else:
                    logger.warning("لا يمكن إرسال الإشعار مباشرة: رمز البوت غير متاح")

            except Exception as e:
                logger.warning(f"لم يتم إرسال الإشعار مباشرة للمستخدم {user_id}: {str(e)}")
                # سيتم إرسال الإشعار لاحقاً من خلال نظام إرسال الإشعارات التلقائي

            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def send_free_day_reminders(self) -> int:
        """
        إرسال تذكيرات باليوم المجاني القادم

        Returns:
            عدد المستخدمين الذين تم إرسال تذكيرات لهم
        """
        try:
            reminder_count = 0

            # الحصول على جميع المستخدمين
            users_ref = self.db.collection('users')
            users = users_ref.get()

            today = datetime.now().date()

            for user in users:
                try:
                    user_id = user.id
                    user_data = user.to_dict()

                    # تجاهل وثائق البيانات الوصفية
                    if user_id.startswith('_'):
                        continue

                    # التحقق من وجود بيانات اليوم المجاني
                    if 'free_day_of_week' not in user_data:
                        continue

                    # الحصول على حالة اليوم المجاني
                    status = self.get_user_free_day_status(user_id)
                    next_free_day = status['next_free_day'].date()

                    # التحقق من أن اليوم المجاني القادم هو غدًا
                    if (next_free_day - today).days == 1:
                        # إرسال تذكير
                        await self.send_free_day_notification(user_id, 'reminder')
                        reminder_count += 1

                except Exception as e:
                    logger.error(f"خطأ في إرسال تذكير اليوم المجاني للمستخدم {user.id}: {str(e)}")
                    continue

            logger.info(f"تم إرسال تذكيرات اليوم المجاني لـ {reminder_count} مستخدم")
            return reminder_count

        except Exception as e:
            logger.error(f"خطأ في إرسال تذكيرات اليوم المجاني: {str(e)}")
            return 0

    def clear_cache(self, user_id: str = None) -> int:
        """
        مسح الذاكرة المؤقتة

        Args:
            user_id: معرف المستخدم (إذا كان None، يتم مسح جميع الذاكرة المؤقتة)

        Returns:
            عدد العناصر التي تم مسحها
        """
        try:
            if user_id:
                # مسح الذاكرة المؤقتة لمستخدم محدد
                if user_id in self._cache:
                    del self._cache[user_id]
                if user_id in self._cache_expiry:
                    del self._cache_expiry[user_id]
                return 1
            else:
                # مسح جميع الذاكرة المؤقتة
                count = len(self._cache)
                self._cache.clear()
                self._cache_expiry.clear()
                return count

        except Exception as e:
            logger.error(f"خطأ في مسح الذاكرة المؤقتة: {str(e)}")
            return 0

    def grant_free_day(self, user_id: str, duration_hours: int = 24) -> bool:
        """
        منح يوم مجاني للمستخدم

        Args:
            user_id: معرف المستخدم
            duration_hours: مدة اليوم المجاني بالساعات (افتراضياً 24 ساعة)

        Returns:
            True إذا تم منح اليوم المجاني بنجاح، False في حالة الفشل
        """
        try:
            # التحقق من وجود المستخدم
            user_ref = self.db.collection('users').document(user_id)
            user_doc = user_ref.get()

            if not user_doc.exists:
                logger.warning(f"المستخدم {user_id} غير موجود في قاعدة البيانات")
                return False

            # تعيين وقت بداية ونهاية اليوم المجاني
            now = datetime.now(pytz.UTC)
            end_time = now + timedelta(hours=duration_hours)

            # تحديث بيانات المستخدم
            user_ref.update({
                'free_day_active': True,
                'free_day_start': now.isoformat(),
                'free_day_end': end_time.isoformat(),
                'last_free_day_used': now.isoformat()
            })

            # مسح الذاكرة المؤقتة للمستخدم
            self.clear_cache(user_id)

            # إرسال إشعار للمستخدم بتفعيل اليوم المجاني
            try:
                import asyncio
                # تشغيل الدالة غير المتزامنة في حلقة الأحداث الحالية أو إنشاء حلقة جديدة
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # إذا كانت الحلقة تعمل، نستخدم create_task
                        asyncio.create_task(self.send_free_day_notification(user_id, 'available'))
                    else:
                        # إذا لم تكن الحلقة تعمل، نشغل الدالة مباشرة
                        loop.run_until_complete(self.send_free_day_notification(user_id, 'available'))
                except RuntimeError:
                    # إذا لم تكن هناك حلقة أحداث، ننشئ واحدة جديدة
                    asyncio.run(self.send_free_day_notification(user_id, 'available'))
            except Exception as e:
                logger.error(f"خطأ في إرسال إشعار اليوم المجاني للمستخدم {user_id}: {str(e)}")

            logger.info(f"تم منح يوم مجاني للمستخدم {user_id} بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في منح يوم مجاني للمستخدم {user_id}: {str(e)}")
            return False

    def grant_free_day_to_all(self, duration_hours: int = 24) -> Dict[str, Any]:
        """
        منح يوم مجاني لجميع المستخدمين غير المشتركين

        Args:
            duration_hours: مدة اليوم المجاني بالساعات (افتراضياً 24 ساعة)

        Returns:
            قاموس يحتوي على إحصائيات العملية
        """
        try:
            # الحصول على جميع المستخدمين
            users_ref = self.db.collection('users').stream()

            stats = {
                'total': 0,
                'success': 0,
                'failed': 0,
                'skipped_subscribers': 0
            }

            for user_doc in users_ref:
                user_id = user_doc.id
                user_data = user_doc.to_dict()

                # تجاهل وثائق البيانات الوصفية
                if user_id.startswith('_'):
                    continue

                stats['total'] += 1

                # تخطي المستخدمين المشتركين
                if user_data.get('is_subscribed', False):
                    stats['skipped_subscribers'] += 1
                    continue

                # منح يوم مجاني
                success = self.grant_free_day(user_id, duration_hours)
                if success:
                    stats['success'] += 1
                else:
                    stats['failed'] += 1

            logger.info(f"تم منح يوم مجاني لـ {stats['success']} مستخدم من أصل {stats['total']} مستخدم")
            return stats

        except Exception as e:
            logger.error(f"خطأ في منح يوم مجاني لجميع المستخدمين: {str(e)}")
            return {
                'total': 0,
                'success': 0,
                'failed': 0,
                'skipped_subscribers': 0,
                'error': str(e)
            }

    def has_active_free_day(self, user_id: str) -> bool:
        """
        دالة موحدة للتحقق من وجود يوم مجاني نشط للمستخدم
        تجمع بين منطق is_today_free_day و is_free_day_active

        Args:
            user_id: معرف المستخدم

        Returns:
            True إذا كان لدى المستخدم يوم مجاني نشط، False خلاف ذلك
        """
        try:
            # التحقق من اليوم المجاني النشط أولاً
            if self.is_free_day_active(user_id):
                return True

            # التحقق من أن اليوم الحالي هو اليوم المجاني وأنه لم يتم استخدامه
            if self.is_today_free_day(user_id):
                # تفعيل اليوم المجاني تلقائياً
                try:
                    import asyncio
                    # تشغيل الدالة غير المتزامنة
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            asyncio.create_task(self.activate_free_day(user_id))
                        else:
                            loop.run_until_complete(self.activate_free_day(user_id))
                    except RuntimeError:
                        asyncio.run(self.activate_free_day(user_id))
                    return True
                except Exception as e:
                    logger.error(f"خطأ في تفعيل اليوم المجاني التلقائي للمستخدم {user_id}: {str(e)}")

            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    def is_free_day_active(self, user_id: str) -> bool:
        """
        التحقق مما إذا كان اليوم المجاني نشطاً للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            True إذا كان اليوم المجاني نشطاً، False خلاف ذلك
        """
        try:
            # التحقق من وجود المستخدم
            user_ref = self.db.collection('users').document(user_id)
            user_doc = user_ref.get()

            if not user_doc.exists:
                return False

            user_data = user_doc.to_dict()

            # التحقق من النظام الجديد أولاً (free_day_active مع أوقات محددة)
            if user_data.get('free_day_active', False):
                # التحقق من وقت انتهاء اليوم المجاني
                end_time_str = user_data.get('free_day_end')
                if end_time_str:
                    try:
                        # تحويل الوقت مع معالجة المناطق الزمنية
                        if end_time_str.endswith('+00:00') or 'T' in end_time_str:
                            end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                            if end_time.tzinfo is None:
                                end_time = end_time.replace(tzinfo=pytz.UTC)
                        else:
                            end_time = datetime.fromisoformat(end_time_str)
                            end_time = end_time.replace(tzinfo=pytz.UTC)

                        # التحقق مما إذا كان الوقت الحالي قبل وقت الانتهاء
                        now = datetime.now(pytz.UTC)
                        if now < end_time:
                            return True
                        else:
                            # انتهى الوقت، إلغاء التفعيل
                            user_ref.update({'free_day_active': False})
                            return False
                    except Exception as e:
                        logger.warning(f"خطأ في تحليل وقت انتهاء اليوم المجاني للمستخدم {user_id}: {str(e)}")

            # التحقق من النظام القديم (is_free_day_active مع اليوم الأسبوعي)
            if user_data.get('is_free_day_active', False):
                # التحقق من أن اليوم الحالي هو اليوم المجاني المحدد
                free_day = user_data.get('free_day_of_week', 0)
                today_weekday = datetime.now().weekday()

                if today_weekday == free_day:
                    # التحقق من آخر استخدام لضمان عدم الاستخدام المتكرر في نفس الأسبوع
                    last_used_str = user_data.get('last_free_day_used')
                    if last_used_str:
                        try:
                            last_used = datetime.fromisoformat(last_used_str).date()
                            today = datetime.now().date()
                            # إذا كان آخر استخدام في نفس الأسبوع، إلغاء التفعيل
                            if last_used.isocalendar()[1] == today.isocalendar()[1]:
                                return True  # نفس الأسبوع، اليوم المجاني لا يزال نشطاً
                        except Exception as e:
                            logger.warning(f"خطأ في تحليل تاريخ آخر استخدام للمستخدم {user_id}: {str(e)}")
                    return True
                else:
                    # ليس اليوم المجاني، إلغاء التفعيل
                    user_ref.update({'is_free_day_active': False})
                    return False

            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة اليوم المجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def delete_free_day_notification(self, notification_id: str) -> bool:
        """
        حذف إشعار اليوم المجاني بواسطة معرف الإشعار

        Args:
            notification_id: معرف الإشعار

        Returns:
            True إذا تم حذف الإشعار بنجاح، False خلاف ذلك
        """
        try:
            # حذف الإشعار من قاعدة البيانات
            notification_ref = self.db.collection('notifications').document(notification_id)
            notification_doc = notification_ref.get()

            if not notification_doc.exists:
                logger.warning(f"الإشعار {notification_id} غير موجود في قاعدة البيانات")
                return False

            notification_data = notification_doc.to_dict()
            notification_type = notification_data.get('type', '')

            # التحقق من أن الإشعار متعلق باليوم المجاني
            if not notification_type.startswith('free_day_'):
                logger.warning(f"الإشعار {notification_id} ليس إشعار يوم مجاني")
                return False

            # حذف الإشعار
            notification_ref.delete()
            logger.info(f"تم حذف إشعار اليوم المجاني {notification_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف إشعار اليوم المجاني {notification_id}: {str(e)}")
            return False

    async def cleanup_free_day_notifications(self, days_old: int = 7) -> int:
        """
        تنظيف إشعارات اليوم المجاني القديمة

        Args:
            days_old: عدد الأيام التي يعتبر بعدها الإشعار قديمًا (افتراضيًا 7 أيام)

        Returns:
            عدد الإشعارات التي تم حذفها
        """
        try:
            # تحديد تاريخ قديم
            cutoff_date = (datetime.now() - timedelta(days=days_old)).isoformat()
            
            # الحصول على جميع إشعارات اليوم المجاني القديمة
            from google.cloud.firestore import FieldFilter
            old_notifications = self.db.collection('notifications').where(filter=FieldFilter('created_at', '<', cutoff_date)).get()
            
            deleted_count = 0
            for notification in old_notifications:
                notification_data = notification.to_dict()
                notification_type = notification_data.get('type', '')
                
                # التحقق من أن الإشعار متعلق باليوم المجاني
                if notification_type.startswith('free_day_'):
                    # حذف الإشعار
                    notification.reference.delete()
                    deleted_count += 1
            
            logger.info(f"تم حذف {deleted_count} إشعار يوم مجاني قديم")
            return deleted_count
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف إشعارات اليوم المجاني القديمة: {str(e)}")
            return 0

# إنشاء نسخة عامة من النظام
free_day_system = FreeDaySystem()
