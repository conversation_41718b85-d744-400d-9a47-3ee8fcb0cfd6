"""
اختبارات نظام اليوم المجاني
"""

import asyncio
import logging
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.free_day_system import FreeDaySystem

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockFirestore:
    """محاكي قاعدة البيانات للاختبار"""
    
    def __init__(self):
        self.data = {}
    
    def collection(self, name):
        return MockCollection(self.data, name)

class MockCollection:
    def __init__(self, data, name):
        self.data = data
        self.name = name
        if name not in self.data:
            self.data[name] = {}
    
    def document(self, doc_id):
        return MockDocument(self.data[self.name], doc_id)
    
    def get(self):
        docs = []
        for doc_id, doc_data in self.data[self.name].items():
            mock_doc = Mock()
            mock_doc.id = doc_id
            mock_doc.to_dict.return_value = doc_data
            mock_doc.reference = MockDocument(self.data[self.name], doc_id)
            docs.append(mock_doc)
        return docs

class MockDocument:
    def __init__(self, collection_data, doc_id):
        self.collection_data = collection_data
        self.doc_id = doc_id
    
    def get(self):
        mock_doc = Mock()
        mock_doc.exists = self.doc_id in self.collection_data
        if mock_doc.exists:
            mock_doc.to_dict.return_value = self.collection_data[self.doc_id]
        return mock_doc
    
    def set(self, data):
        self.collection_data[self.doc_id] = data
    
    def update(self, data):
        if self.doc_id not in self.collection_data:
            self.collection_data[self.doc_id] = {}
        self.collection_data[self.doc_id].update(data)

async def test_free_day_system():
    """اختبار شامل لنظام اليوم المجاني"""
    
    print("🧪 بدء اختبارات نظام اليوم المجاني...")
    
    # إنشاء قاعدة بيانات وهمية
    mock_db = MockFirestore()
    
    # إنشاء نظام اليوم المجاني
    free_day_system = FreeDaySystem(mock_db)
    
    # معرف مستخدم للاختبار
    test_user_id = "test_user_123"
    
    # اختبار 1: إنشاء مستخدم جديد
    print("\n📝 اختبار 1: إنشاء مستخدم جديد")
    status = free_day_system.get_user_free_day_status(test_user_id)
    print(f"✅ حالة المستخدم الجديد: {status}")
    
    # اختبار 2: التحقق من اليوم المجاني
    print("\n📝 اختبار 2: التحقق من اليوم المجاني")
    is_today = free_day_system.is_today_free_day(test_user_id)
    is_active = free_day_system.is_free_day_active(test_user_id)
    has_active = free_day_system.has_active_free_day(test_user_id)
    print(f"✅ هل اليوم هو اليوم المجاني: {is_today}")
    print(f"✅ هل اليوم المجاني نشط: {is_active}")
    print(f"✅ هل لديه يوم مجاني نشط: {has_active}")
    
    # اختبار 3: تفعيل اليوم المجاني
    print("\n📝 اختبار 3: تفعيل اليوم المجاني")
    
    # تعيين اليوم الحالي كيوم مجاني
    current_weekday = datetime.now().weekday()
    await free_day_system.set_free_day(test_user_id, current_weekday)
    
    # محاولة تفعيل اليوم المجاني
    activation_result = await free_day_system.activate_free_day(test_user_id)
    print(f"✅ نتيجة تفعيل اليوم المجاني: {activation_result}")
    
    # التحقق من الحالة بعد التفعيل
    is_active_after = free_day_system.is_free_day_active(test_user_id)
    has_active_after = free_day_system.has_active_free_day(test_user_id)
    print(f"✅ هل اليوم المجاني نشط بعد التفعيل: {is_active_after}")
    print(f"✅ هل لديه يوم مجاني نشط بعد التفعيل: {has_active_after}")
    
    # اختبار 4: منح يوم مجاني مؤقت
    print("\n📝 اختبار 4: منح يوم مجاني مؤقت")
    
    # إنشاء مستخدم جديد للاختبار
    temp_user_id = "temp_user_456"
    
    # إضافة المستخدم إلى قاعدة البيانات
    mock_db.data['users'][temp_user_id] = {
        'userId': temp_user_id,
        'username': 'test_user',
        'subscriptionStatus': 'غير مشترك'
    }
    
    # منح يوم مجاني مؤقت
    grant_result = free_day_system.grant_free_day(temp_user_id, 24)
    print(f"✅ نتيجة منح اليوم المجاني المؤقت: {grant_result}")
    
    # التحقق من الحالة
    temp_is_active = free_day_system.is_free_day_active(temp_user_id)
    temp_has_active = free_day_system.has_active_free_day(temp_user_id)
    print(f"✅ هل اليوم المجاني المؤقت نشط: {temp_is_active}")
    print(f"✅ هل لديه يوم مجاني نشط (مؤقت): {temp_has_active}")
    
    # اختبار 5: إلغاء تفعيل اليوم المجاني
    print("\n📝 اختبار 5: إلغاء تفعيل اليوم المجاني")
    deactivation_result = await free_day_system.deactivate_free_day(test_user_id)
    print(f"✅ نتيجة إلغاء تفعيل اليوم المجاني: {deactivation_result}")
    
    # التحقق من الحالة بعد الإلغاء
    is_active_after_deactivation = free_day_system.is_free_day_active(test_user_id)
    has_active_after_deactivation = free_day_system.has_active_free_day(test_user_id)
    print(f"✅ هل اليوم المجاني نشط بعد الإلغاء: {is_active_after_deactivation}")
    print(f"✅ هل لديه يوم مجاني نشط بعد الإلغاء: {has_active_after_deactivation}")
    
    # اختبار 6: تنظيف الأيام المجانية المنتهية
    print("\n📝 اختبار 6: تنظيف الأيام المجانية المنتهية")
    
    # إنشاء يوم مجاني منتهي الصلاحية
    expired_user_id = "expired_user_789"
    past_time = datetime.now() - timedelta(hours=1)
    mock_db.data['users'][expired_user_id] = {
        'userId': expired_user_id,
        'free_day_active': True,
        'free_day_start': past_time.isoformat(),
        'free_day_end': past_time.isoformat()
    }
    
    # تنظيف الأيام المنتهية
    cleaned_count = await free_day_system.cleanup_expired_free_days()
    print(f"✅ عدد الأيام المجانية المنتهية التي تم تنظيفها: {cleaned_count}")
    
    # التحقق من أن اليوم المنتهي تم إلغاؤه
    expired_is_active = free_day_system.is_free_day_active(expired_user_id)
    print(f"✅ هل اليوم المجاني المنتهي لا يزال نشطاً: {expired_is_active}")
    
    print("\n🎉 انتهت جميع الاختبارات بنجاح!")
    
    # طباعة ملخص البيانات
    print("\n📊 ملخص بيانات قاعدة البيانات:")
    for collection_name, collection_data in mock_db.data.items():
        print(f"📁 {collection_name}:")
        for doc_id, doc_data in collection_data.items():
            print(f"  📄 {doc_id}: {doc_data}")

if __name__ == "__main__":
    # تشغيل الاختبارات
    asyncio.run(test_free_day_system())
