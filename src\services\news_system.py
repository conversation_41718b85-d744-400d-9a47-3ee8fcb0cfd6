"""
نظام الأخبار الذكي المدعوم بالذكاء الاصطناعي
يجلب الأخبار من مصادر متعددة ويحللها باستخدام Gemini AI
"""

import logging
import asyncio
import aiohttp
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# إعداد التسجيل
logger = logging.getLogger(__name__)

class NewsType(Enum):
    """أنواع الأخبار"""
    PRICE_PREDICTION = "price_prediction"
    NEW_COIN_ALERT = "new_coin_alert"
    GENERAL_CRYPTO = "general_crypto"

class NewsSource(Enum):
    """مصادر الأخبار"""
    BINANCE = "binance"
    COINDESK = "coindesk"
    COINTELEGRAPH = "cointelegraph"
    CRYPTO_NEWS = "crypto_news"

@dataclass
class NewsItem:
    """عنصر خبر واحد"""
    id: str
    title: str
    content: str
    source: NewsSource
    published_at: datetime
    url: Optional[str] = None
    symbols: List[str] = None
    sentiment: Optional[str] = None
    ai_analysis: Optional[str] = None
    trading_recommendation: Optional[str] = None
    confidence_score: Optional[float] = None

@dataclass
class TradingRecommendation:
    """توصية تداول"""
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    reasoning: str = ""

class NewsSystem:
    """نظام الأخبار الذكي"""
    
    def __init__(self, db=None, gemini_api_key: str = None):
        """
        تهيئة نظام الأخبار
        
        Args:
            db: قاعدة بيانات Firestore
            gemini_api_key: مفتاح API لـ Gemini
        """
        self.db = db
        self.gemini_api_key = gemini_api_key
        self.news_cache = {}
        self.cache_expiry = {}
        
        # URLs للمصادر المختلفة
        self.news_sources = {
            NewsSource.BINANCE: {
                'url': 'https://www.binance.com/bapi/composite/v1/public/cms/article/list/query',
                'method': 'POST',
                'headers': {'Content-Type': 'application/json'},
                'params': {
                    'type': 1,
                    'catalogId': 48,
                    'pageNo': 1,
                    'pageSize': 20
                }
            },
            NewsSource.COINDESK: {
                'url': 'https://api.coindesk.com/v1/news/articles',
                'method': 'GET',
                'headers': {'User-Agent': 'TradingBot/1.0'},
                'params': {'limit': 20}
            },
            NewsSource.CRYPTO_NEWS: {
                'url': 'https://cryptonews.net/api/v1/category/altcoin',
                'method': 'GET',
                'headers': {'User-Agent': 'TradingBot/1.0'},
                'params': {'limit': 20}
            }
        }
    
    async def fetch_news_from_binance(self) -> List[NewsItem]:
        """جلب الأخبار من Binance"""
        try:
            source_config = self.news_sources[NewsSource.BINANCE]
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    source_config['url'],
                    headers=source_config['headers'],
                    json=source_config['params']
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        news_items = []
                        
                        if 'data' in data and 'catalogs' in data['data']:
                            for article in data['data']['catalogs']:
                                news_item = NewsItem(
                                    id=f"binance_{article.get('id', '')}",
                                    title=article.get('title', ''),
                                    content=article.get('body', ''),
                                    source=NewsSource.BINANCE,
                                    published_at=datetime.fromtimestamp(
                                        article.get('releaseDate', 0) / 1000
                                    ),
                                    url=f"https://www.binance.com/en/support/announcement/{article.get('code', '')}"
                                )
                                news_items.append(news_item)
                        
                        logger.info(f"تم جلب {len(news_items)} خبر من Binance")
                        return news_items
                    else:
                        logger.error(f"فشل في جلب الأخبار من Binance: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار من Binance: {str(e)}")
            return []
    
    async def fetch_crypto_prices(self, symbols: List[str]) -> Dict[str, float]:
        """جلب أسعار العملات الرقمية من Binance"""
        try:
            if not symbols:
                return {}
            
            # تحويل الرموز إلى تنسيق Binance
            binance_symbols = [f"{symbol}USDT" for symbol in symbols if symbol != "USDT"]
            
            if not binance_symbols:
                return {}
            
            symbols_param = ','.join([f'"{symbol}"' for symbol in binance_symbols])
            url = f"https://api.binance.com/api/v3/ticker/price"
            
            async with aiohttp.ClientSession() as session:
                prices = {}
                
                # جلب الأسعار لكل رمز على حدة لتجنب مشاكل الطلبات الكبيرة
                for symbol in binance_symbols:
                    try:
                        async with session.get(f"{url}?symbol={symbol}") as response:
                            if response.status == 200:
                                data = await response.json()
                                clean_symbol = symbol.replace('USDT', '')
                                prices[clean_symbol] = float(data.get('price', 0))
                    except Exception as e:
                        logger.warning(f"فشل في جلب سعر {symbol}: {str(e)}")
                
                logger.info(f"تم جلب أسعار {len(prices)} عملة")
                return prices
                
        except Exception as e:
            logger.error(f"خطأ في جلب أسعار العملات: {str(e)}")
            return {}
    
    async def analyze_news_with_ai(self, news_item: NewsItem, current_prices: Dict[str, float] = None, lang: str = 'ar') -> NewsItem:
        """تحليل الخبر باستخدام Gemini AI"""
        try:
            if not self.gemini_api_key:
                logger.warning("مفتاح Gemini AI غير متوفر")
                return news_item

            # إعداد النص للتحليل حسب اللغة
            if lang == 'ar':
                analysis_prompt = f"""
                قم بتحليل هذا الخبر المتعلق بالعملات الرقمية وقدم:
                1. تحليل المشاعر (إيجابي/سلبي/محايد)
                2. العملات المذكورة في الخبر
                3. توصية تداول (شراء/بيع/انتظار)
                4. مستوى الثقة (0-100%)
                5. تفسير مختصر للتوصية
                6. تصنيف نوع الخبر (توقع أسعار/عملة جديدة/أخبار عامة)

                العنوان: {news_item.title}
                المحتوى: {news_item.content[:1000]}...

                الأسعار الحالية: {current_prices if current_prices else "غير متوفرة"}

                قدم الإجابة بتنسيق JSON باللغة العربية:
                {{
                    "sentiment": "positive/negative/neutral",
                    "mentioned_symbols": ["BTC", "ETH"],
                    "news_type": "price_prediction/new_coin_alert/general_crypto",
                    "trading_recommendation": {{
                        "action": "BUY/SELL/HOLD",
                        "confidence": 85,
                        "target_price": 50000,
                        "stop_loss": 45000,
                        "reasoning": "تفسير التوصية بالعربية"
                    }},
                    "analysis": "تحليل مفصل للخبر بالعربية",
                    "summary": "ملخص الخبر في جملتين"
                }}
                """
            else:
                analysis_prompt = f"""
                Analyze this cryptocurrency news and provide:
                1. Sentiment analysis (positive/negative/neutral)
                2. Mentioned cryptocurrencies
                3. Trading recommendation (buy/sell/hold)
                4. Confidence level (0-100%)
                5. Brief reasoning for the recommendation
                6. News type classification (price prediction/new coin alert/general crypto)

                Title: {news_item.title}
                Content: {news_item.content[:1000]}...

                Current Prices: {current_prices if current_prices else "Not available"}

                Provide the answer in JSON format in English:
                {{
                    "sentiment": "positive/negative/neutral",
                    "mentioned_symbols": ["BTC", "ETH"],
                    "news_type": "price_prediction/new_coin_alert/general_crypto",
                    "trading_recommendation": {{
                        "action": "BUY/SELL/HOLD",
                        "confidence": 85,
                        "target_price": 50000,
                        "stop_loss": 45000,
                        "reasoning": "Reasoning for the recommendation in English"
                    }},
                    "analysis": "Detailed analysis of the news in English",
                    "summary": "News summary in two sentences"
                }}
                """

            # استدعاء Gemini AI
            ai_response = await self._call_gemini_api(analysis_prompt)

            if ai_response:
                try:
                    # تنظيف الاستجابة من أي نص إضافي
                    clean_response = ai_response.strip()
                    if clean_response.startswith('```json'):
                        clean_response = clean_response[7:]
                    if clean_response.endswith('```'):
                        clean_response = clean_response[:-3]
                    clean_response = clean_response.strip()

                    analysis_data = json.loads(clean_response)

                    # تحديث عنصر الخبر بالتحليل
                    news_item.sentiment = analysis_data.get('sentiment', 'neutral')
                    news_item.symbols = analysis_data.get('mentioned_symbols', [])
                    news_item.ai_analysis = analysis_data.get('analysis', '')

                    # معالجة التوصية
                    recommendation = analysis_data.get('trading_recommendation', {})
                    if recommendation:
                        news_item.trading_recommendation = recommendation.get('action', 'HOLD')
                        news_item.confidence_score = recommendation.get('confidence', 0) / 100.0

                    logger.info(f"تم تحليل الخبر {news_item.id} بنجاح")

                except json.JSONDecodeError as e:
                    logger.error(f"خطأ في تحليل استجابة AI: {str(e)}")
                    logger.error(f"الاستجابة الخام: {ai_response}")
                    news_item.ai_analysis = ai_response  # حفظ الاستجابة الخام

            return news_item

        except Exception as e:
            logger.error(f"خطأ في تحليل الخبر بالذكاء الاصطناعي: {str(e)}")
            return news_item

    async def get_price_predictions(self, lang: str = 'ar', limit: int = 5) -> List[NewsItem]:
        """الحصول على توقعات الأسعار"""
        try:
            analyzed_news = await self.get_analyzed_news(NewsType.PRICE_PREDICTION, limit * 2)

            # تصفية الأخبار التي تحتوي على توقعات أسعار
            price_predictions = []
            for news in analyzed_news:
                if (news.trading_recommendation and news.trading_recommendation != 'HOLD' and
                    news.confidence_score and news.confidence_score > 0.6):
                    price_predictions.append(news)

            return price_predictions[:limit]

        except Exception as e:
            logger.error(f"خطأ في الحصول على توقعات الأسعار: {str(e)}")
            return []

    async def get_new_coin_alerts(self, lang: str = 'ar', limit: int = 3) -> List[NewsItem]:
        """الحصول على تنبيهات العملات الجديدة"""
        try:
            all_news = await self.get_latest_news(limit=20)

            # البحث عن أخبار العملات الجديدة
            new_coin_keywords = ['new listing', 'new token', 'launch', 'debut', 'إدراج جديد', 'عملة جديدة', 'إطلاق']
            new_coin_news = []

            for news in all_news:
                title_lower = news.title.lower()
                content_lower = news.content.lower()

                if any(keyword in title_lower or keyword in content_lower for keyword in new_coin_keywords):
                    analyzed_news = await self.analyze_news_with_ai(news, lang=lang)
                    new_coin_news.append(analyzed_news)

                    if len(new_coin_news) >= limit:
                        break

            return new_coin_news

        except Exception as e:
            logger.error(f"خطأ في الحصول على تنبيهات العملات الجديدة: {str(e)}")
            return []
    
    async def _call_gemini_api(self, prompt: str) -> Optional[str]:
        """استدعاء Gemini AI API"""
        try:
            if not self.gemini_api_key:
                return None
            
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={self.gemini_api_key}"
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'candidates' in data and len(data['candidates']) > 0:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])
                            if parts:
                                return parts[0].get('text', '')
                    else:
                        logger.error(f"خطأ في استدعاء Gemini API: {response.status}")
                        error_text = await response.text()
                        logger.error(f"تفاصيل الخطأ: {error_text}")
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return None
    
    async def fetch_news_from_coindesk(self) -> List[NewsItem]:
        """جلب الأخبار من CoinDesk"""
        try:
            # استخدام RSS feed بدلاً من API المدفوع
            url = "https://feeds.coindesk.com/rss"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        import xml.etree.ElementTree as ET
                        content = await response.text()
                        root = ET.fromstring(content)

                        news_items = []
                        for item in root.findall('.//item')[:20]:  # أول 20 خبر
                            title = item.find('title').text if item.find('title') is not None else ""
                            description = item.find('description').text if item.find('description') is not None else ""
                            link = item.find('link').text if item.find('link') is not None else ""
                            pub_date = item.find('pubDate').text if item.find('pubDate') is not None else ""

                            # تحويل التاريخ
                            try:
                                from email.utils import parsedate_to_datetime
                                published_at = parsedate_to_datetime(pub_date)
                            except:
                                published_at = datetime.now()

                            news_item = NewsItem(
                                id=f"coindesk_{hash(link)}",
                                title=title,
                                content=description,
                                source=NewsSource.COINDESK,
                                published_at=published_at,
                                url=link
                            )
                            news_items.append(news_item)

                        logger.info(f"تم جلب {len(news_items)} خبر من CoinDesk")
                        return news_items
                    else:
                        logger.error(f"فشل في جلب الأخبار من CoinDesk: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار من CoinDesk: {str(e)}")
            return []

    async def fetch_news_from_crypto_news(self) -> List[NewsItem]:
        """جلب الأخبار من مصادر أخرى للعملات الرقمية"""
        try:
            # استخدام CoinGecko API للأخبار (مجاني)
            url = "https://api.coingecko.com/api/v3/news"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        news_items = []

                        if 'data' in data:
                            for article in data['data'][:20]:  # أول 20 خبر
                                news_item = NewsItem(
                                    id=f"coingecko_{article.get('id', '')}",
                                    title=article.get('title', ''),
                                    content=article.get('description', ''),
                                    source=NewsSource.CRYPTO_NEWS,
                                    published_at=datetime.fromisoformat(
                                        article.get('published_at', '').replace('Z', '+00:00')
                                    ) if article.get('published_at') else datetime.now(),
                                    url=article.get('url', '')
                                )
                                news_items.append(news_item)

                        logger.info(f"تم جلب {len(news_items)} خبر من CoinGecko")
                        return news_items
                    else:
                        logger.error(f"فشل في جلب الأخبار من CoinGecko: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار من CoinGecko: {str(e)}")
            return []

    async def get_latest_news(self, news_type: NewsType = None, limit: int = 10) -> List[NewsItem]:
        """الحصول على آخر الأخبار"""
        try:
            # جلب الأخبار من المصادر المختلفة
            all_news = []

            # جلب من مصادر متعددة بشكل متوازي
            tasks = [
                self.fetch_news_from_binance(),
                self.fetch_news_from_coindesk(),
                self.fetch_news_from_crypto_news()
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, list):
                    all_news.extend(result)
                elif isinstance(result, Exception):
                    logger.warning(f"خطأ في جلب الأخبار من أحد المصادر: {str(result)}")

            # ترتيب الأخبار حسب التاريخ
            all_news.sort(key=lambda x: x.published_at, reverse=True)

            # إزالة الأخبار المكررة
            unique_news = []
            seen_titles = set()
            for news in all_news:
                if news.title not in seen_titles:
                    unique_news.append(news)
                    seen_titles.add(news.title)

            # تحديد الأخبار حسب النوع المطلوب
            if news_type:
                # هنا يمكن إضافة منطق لتصنيف الأخبار حسب النوع
                pass

            # تحديد العدد المطلوب
            return unique_news[:limit]

        except Exception as e:
            logger.error(f"خطأ في الحصول على الأخبار: {str(e)}")
            return []
    
    async def get_analyzed_news(self, news_type: NewsType = None, limit: int = 5) -> List[NewsItem]:
        """الحصول على الأخبار المحللة بالذكاء الاصطناعي"""
        try:
            # جلب آخر الأخبار
            latest_news = await self.get_latest_news(news_type, limit)
            
            if not latest_news:
                return []
            
            # جلب الأسعار الحالية للعملات المذكورة
            all_symbols = set()
            for news in latest_news:
                if news.symbols:
                    all_symbols.update(news.symbols)
            
            current_prices = await self.fetch_crypto_prices(list(all_symbols)) if all_symbols else {}
            
            # تحليل كل خبر بالذكاء الاصطناعي
            analyzed_news = []
            for news in latest_news:
                analyzed_news_item = await self.analyze_news_with_ai(news, current_prices)
                analyzed_news.append(analyzed_news_item)
                
                # تأخير قصير لتجنب تجاوز حدود API
                await asyncio.sleep(1)
            
            return analyzed_news
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الأخبار: {str(e)}")
            return []

# إنشاء نسخة عامة من النظام
news_system = None

def initialize_news_system(db=None, gemini_api_key: str = None):
    """تهيئة نظام الأخبار"""
    global news_system
    news_system = NewsSystem(db, gemini_api_key)
    logger.info("✅ تم تهيئة نظام الأخبار بنجاح")
    return news_system
