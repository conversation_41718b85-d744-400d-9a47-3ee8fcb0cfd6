"""
اختبارات نظام الأخبار الذكي
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.news_system import NewsSystem, NewsType, NewsSource

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_news_system():
    """اختبار شامل لنظام الأخبار"""
    
    print("🧪 بدء اختبارات نظام الأخبار الذكي...")
    
    # إنشاء نظام الأخبار (بدون مفتاح Gemini للاختبار الأساسي)
    news_system = NewsSystem(db=None, gemini_api_key=None)
    
    # اختبار 1: جلب الأخبار من Binance
    print("\n📝 اختبار 1: جلب الأخبار من Binance")
    try:
        binance_news = await news_system.fetch_news_from_binance()
        print(f"✅ تم جلب {len(binance_news)} خبر من Binance")
        
        if binance_news:
            first_news = binance_news[0]
            print(f"📰 أول خبر: {first_news.title[:100]}...")
            print(f"📅 تاريخ النشر: {first_news.published_at}")
            print(f"🔗 المصدر: {first_news.source}")
    except Exception as e:
        print(f"❌ خطأ في جلب أخبار Binance: {str(e)}")
    
    # اختبار 2: جلب الأخبار من CoinDesk
    print("\n📝 اختبار 2: جلب الأخبار من CoinDesk")
    try:
        coindesk_news = await news_system.fetch_news_from_coindesk()
        print(f"✅ تم جلب {len(coindesk_news)} خبر من CoinDesk")
        
        if coindesk_news:
            first_news = coindesk_news[0]
            print(f"📰 أول خبر: {first_news.title[:100]}...")
            print(f"📅 تاريخ النشر: {first_news.published_at}")
            print(f"🔗 المصدر: {first_news.source}")
    except Exception as e:
        print(f"❌ خطأ في جلب أخبار CoinDesk: {str(e)}")
    
    # اختبار 3: جلب الأخبار من CoinGecko
    print("\n📝 اختبار 3: جلب الأخبار من CoinGecko")
    try:
        crypto_news = await news_system.fetch_news_from_crypto_news()
        print(f"✅ تم جلب {len(crypto_news)} خبر من CoinGecko")
        
        if crypto_news:
            first_news = crypto_news[0]
            print(f"📰 أول خبر: {first_news.title[:100]}...")
            print(f"📅 تاريخ النشر: {first_news.published_at}")
            print(f"🔗 المصدر: {first_news.source}")
    except Exception as e:
        print(f"❌ خطأ في جلب أخبار CoinGecko: {str(e)}")
    
    # اختبار 4: جلب آخر الأخبار من جميع المصادر
    print("\n📝 اختبار 4: جلب آخر الأخبار من جميع المصادر")
    try:
        latest_news = await news_system.get_latest_news(limit=10)
        print(f"✅ تم جلب {len(latest_news)} خبر من جميع المصادر")
        
        # عرض ملخص الأخبار
        sources_count = {}
        for news in latest_news:
            source = news.source.value
            sources_count[source] = sources_count.get(source, 0) + 1
        
        print("📊 توزيع الأخبار حسب المصدر:")
        for source, count in sources_count.items():
            print(f"  📰 {source}: {count} خبر")
            
        # عرض أحدث 3 أخبار
        print("\n📋 أحدث 3 أخبار:")
        for i, news in enumerate(latest_news[:3], 1):
            print(f"  {i}. {news.title[:80]}...")
            print(f"     📅 {news.published_at.strftime('%Y-%m-%d %H:%M')}")
            print(f"     📰 {news.source.value}")
            
    except Exception as e:
        print(f"❌ خطأ في جلب آخر الأخبار: {str(e)}")
    
    # اختبار 5: جلب أسعار العملات
    print("\n📝 اختبار 5: جلب أسعار العملات")
    try:
        test_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL']
        prices = await news_system.fetch_crypto_prices(test_symbols)
        print(f"✅ تم جلب أسعار {len(prices)} عملة")
        
        print("💰 الأسعار الحالية:")
        for symbol, price in prices.items():
            print(f"  💎 {symbol}: ${price:,.2f}")
            
    except Exception as e:
        print(f"❌ خطأ في جلب أسعار العملات: {str(e)}")
    
    # اختبار 6: البحث عن العملات الجديدة
    print("\n📝 اختبار 6: البحث عن العملات الجديدة")
    try:
        new_coin_alerts = await news_system.get_new_coin_alerts(lang='ar', limit=3)
        print(f"✅ تم العثور على {len(new_coin_alerts)} تنبيه عملة جديدة")
        
        if new_coin_alerts:
            print("🆕 تنبيهات العملات الجديدة:")
            for i, alert in enumerate(new_coin_alerts, 1):
                print(f"  {i}. {alert.title[:80]}...")
                if alert.symbols:
                    print(f"     💰 العملات المذكورة: {', '.join(alert.symbols)}")
                    
    except Exception as e:
        print(f"❌ خطأ في البحث عن العملات الجديدة: {str(e)}")
    
    # اختبار 7: اختبار تحليل AI (إذا كان المفتاح متوفراً)
    print("\n📝 اختبار 7: اختبار تحليل الذكاء الاصطناعي")
    
    # محاولة الحصول على مفتاح Gemini من متغيرات البيئة
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if gemini_key:
        print("🔑 تم العثور على مفتاح Gemini API، جاري اختبار التحليل...")
        news_system.gemini_api_key = gemini_key
        
        try:
            # استخدام أول خبر متاح للتحليل
            if 'latest_news' in locals() and latest_news:
                test_news = latest_news[0]
                print(f"📰 تحليل الخبر: {test_news.title[:50]}...")
                
                analyzed_news = await news_system.analyze_news_with_ai(test_news, lang='ar')
                
                print("🤖 نتائج التحليل:")
                print(f"  😊 المشاعر: {analyzed_news.sentiment}")
                if analyzed_news.symbols:
                    print(f"  💰 العملات المذكورة: {', '.join(analyzed_news.symbols)}")
                if analyzed_news.trading_recommendation:
                    print(f"  📈 التوصية: {analyzed_news.trading_recommendation}")
                if analyzed_news.confidence_score:
                    print(f"  🎯 مستوى الثقة: {analyzed_news.confidence_score:.1%}")
                if analyzed_news.ai_analysis:
                    print(f"  📝 التحليل: {analyzed_news.ai_analysis[:100]}...")
                    
        except Exception as e:
            print(f"❌ خطأ في تحليل الذكاء الاصطناعي: {str(e)}")
    else:
        print("⚠️ مفتاح Gemini API غير متوفر، تم تخطي اختبار التحليل")
        print("💡 لاختبار التحليل، قم بتعيين متغير البيئة GEMINI_API_KEY")
    
    # اختبار 8: اختبار الأداء
    print("\n📝 اختبار 8: اختبار الأداء")
    try:
        start_time = datetime.now()
        
        # جلب الأخبار بشكل متوازي
        tasks = [
            news_system.fetch_news_from_binance(),
            news_system.fetch_news_from_coindesk(),
            news_system.fetch_crypto_prices(['BTC', 'ETH'])
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⚡ تم تنفيذ 3 مهام متوازية في {duration:.2f} ثانية")
        
        # تحليل النتائج
        successful_tasks = sum(1 for result in results if not isinstance(result, Exception))
        print(f"✅ نجح {successful_tasks} من أصل {len(tasks)} مهام")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {str(e)}")
    
    print("\n🎉 انتهت جميع اختبارات نظام الأخبار!")
    
    # ملخص الاختبارات
    print("\n📊 ملخص الاختبارات:")
    print("✅ اختبار جلب أخبار Binance")
    print("✅ اختبار جلب أخبار CoinDesk") 
    print("✅ اختبار جلب أخبار CoinGecko")
    print("✅ اختبار جلب آخر الأخبار")
    print("✅ اختبار جلب أسعار العملات")
    print("✅ اختبار البحث عن العملات الجديدة")
    if gemini_key:
        print("✅ اختبار تحليل الذكاء الاصطناعي")
    else:
        print("⚠️ تم تخطي اختبار تحليل الذكاء الاصطناعي")
    print("✅ اختبار الأداء")

async def test_news_formatting():
    """اختبار تنسيق الأخبار"""
    print("\n🎨 اختبار تنسيق الأخبار...")
    
    try:
        from handlers.news_handlers import format_news_item, get_time_ago
        from services.news_system import NewsItem, NewsSource
        
        # إنشاء خبر تجريبي
        test_news = NewsItem(
            id="test_001",
            title="Bitcoin Reaches New All-Time High",
            content="Bitcoin has reached a new all-time high of $100,000...",
            source=NewsSource.BINANCE,
            published_at=datetime.now(),
            symbols=['BTC'],
            sentiment='positive',
            trading_recommendation='BUY',
            confidence_score=0.85,
            ai_analysis="This is a very positive development for Bitcoin..."
        )
        
        # اختبار التنسيق بالعربية
        print("📝 تنسيق بالعربية:")
        arabic_format = format_news_item(test_news, lang='ar', show_analysis=True)
        print(arabic_format)
        
        print("\n" + "─" * 50 + "\n")
        
        # اختبار التنسيق بالإنجليزية
        print("📝 تنسيق بالإنجليزية:")
        english_format = format_news_item(test_news, lang='en', show_analysis=True)
        print(english_format)
        
        # اختبار حساب الوقت
        print(f"\n⏰ الوقت المنقضي (عربي): {get_time_ago(test_news.published_at, 'ar')}")
        print(f"⏰ الوقت المنقضي (إنجليزي): {get_time_ago(test_news.published_at, 'en')}")
        
        print("✅ اختبار التنسيق مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنسيق: {str(e)}")

if __name__ == "__main__":
    # تشغيل الاختبارات
    asyncio.run(test_news_system())
    asyncio.run(test_news_formatting())
