"""
نظام التخزين المؤقت الذكي للأخبار
يحفظ الأخبار بذكاء ويقلل من الحاجة لطلبات API متكررة
"""

import asyncio
import logging
import hashlib
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import OrderedDict
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """مدخل في التخزين المؤقت"""
    key: str
    data: Any
    created_at: datetime
    expires_at: datetime
    access_count: int = 0
    last_accessed: datetime = None
    priority: int = 1  # 1=low, 2=medium, 3=high
    size_bytes: int = 0
    source: str = "unknown"
    
    def is_expired(self) -> bool:
        """التحقق من انتهاء صلاحية المدخل"""
        return datetime.now() > self.expires_at
    
    def is_stale(self, staleness_threshold: timedelta = timedelta(minutes=30)) -> bool:
        """التحقق من قدم البيانات"""
        return datetime.now() - self.created_at > staleness_threshold
    
    def update_access(self):
        """تحديث معلومات الوصول"""
        self.access_count += 1
        self.last_accessed = datetime.now()

class IntelligentNewsCache:
    """نظام التخزين المؤقت الذكي للأخبار"""
    
    def __init__(self, max_size_mb: int = 100, db=None):
        self.max_size_bytes = max_size_mb * 1024 * 1024  # تحويل إلى بايت
        self.current_size_bytes = 0
        self.db = db
        
        # التخزين المؤقت الرئيسي
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # فهارس للبحث السريع
        self.source_index: Dict[str, List[str]] = {}  # مفاتيح حسب المصدر
        self.time_index: Dict[str, List[str]] = {}    # مفاتيح حسب الوقت
        self.priority_index: Dict[int, List[str]] = {1: [], 2: [], 3: []}  # مفاتيح حسب الأولوية
        
        # إحصائيات الأداء
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0,
            'cache_efficiency': 0.0,
            'average_response_time': 0.0,
            'memory_usage_mb': 0.0
        }
        
        # إعدادات التخزين المؤقت
        self.default_ttl = {
            'news': timedelta(minutes=15),      # الأخبار العادية
            'breaking_news': timedelta(minutes=5),  # الأخبار العاجلة
            'analysis': timedelta(minutes=30),   # التحليلات
            'prices': timedelta(minutes=2),     # الأسعار
            'general': timedelta(minutes=10)    # عام
        }
        
        # خيوط للمعالجة غير المتزامنة
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.lock = threading.RLock()
        
        # جدولة تنظيف التخزين المؤقت
        self._cleanup_task = None
        
    async def get(self, key: str, default=None) -> Any:
        """الحصول على قيمة من التخزين المؤقت"""
        start_time = datetime.now()
        
        with self.lock:
            self.stats['total_requests'] += 1
            
            if key in self.cache:
                entry = self.cache[key]
                
                # التحقق من انتهاء الصلاحية
                if entry.is_expired():
                    await self._remove_entry(key)
                    self.stats['misses'] += 1
                    return default
                
                # تحديث معلومات الوصول
                entry.update_access()
                
                # نقل إلى النهاية (LRU)
                self.cache.move_to_end(key)
                
                self.stats['hits'] += 1
                
                # تحديث وقت الاستجابة
                response_time = (datetime.now() - start_time).total_seconds() * 1000
                self._update_response_time(response_time)
                
                return entry.data
            else:
                self.stats['misses'] += 1
                return default
    
    async def set(self, key: str, data: Any, ttl: timedelta = None, 
                  priority: int = 1, source: str = "unknown") -> bool:
        """حفظ قيمة في التخزين المؤقت"""
        if ttl is None:
            ttl = self.default_ttl.get(source, self.default_ttl['general'])
        
        # حساب حجم البيانات
        data_size = self._calculate_size(data)
        
        # التحقق من الحد الأقصى للحجم
        if data_size > self.max_size_bytes:
            logger.warning(f"البيانات كبيرة جداً للتخزين المؤقت: {data_size} bytes")
            return False
        
        with self.lock:
            # إزالة المدخل القديم إذا كان موجوداً
            if key in self.cache:
                await self._remove_entry(key)
            
            # التأكد من وجود مساحة كافية
            await self._ensure_space(data_size)
            
            # إنشاء مدخل جديد
            entry = CacheEntry(
                key=key,
                data=data,
                created_at=datetime.now(),
                expires_at=datetime.now() + ttl,
                priority=priority,
                size_bytes=data_size,
                source=source
            )
            
            # إضافة إلى التخزين المؤقت
            self.cache[key] = entry
            self.current_size_bytes += data_size
            
            # تحديث الفهارس
            await self._update_indexes(key, entry)
            
            # حفظ في قاعدة البيانات إذا كانت متوفرة
            if self.db and priority >= 2:  # حفظ الأولويات المتوسطة والعالية فقط
                await self._persist_to_db(key, entry)
            
            return True
    
    async def get_by_source(self, source: str, limit: int = 10) -> List[Tuple[str, Any]]:
        """الحصول على البيانات حسب المصدر"""
        with self.lock:
            keys = self.source_index.get(source, [])
            results = []
            
            for key in keys[:limit]:
                if key in self.cache:
                    entry = self.cache[key]
                    if not entry.is_expired():
                        entry.update_access()
                        results.append((key, entry.data))
            
            return results
    
    async def get_recent(self, minutes: int = 30, limit: int = 20) -> List[Tuple[str, Any]]:
        """الحصول على البيانات الحديثة"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        results = []
        
        with self.lock:
            for key, entry in self.cache.items():
                if entry.created_at >= cutoff_time and not entry.is_expired():
                    entry.update_access()
                    results.append((key, entry.data))
                    
                    if len(results) >= limit:
                        break
        
        # ترتيب حسب الوقت (الأحدث أولاً)
        results.sort(key=lambda x: self.cache[x[0]].created_at, reverse=True)
        return results
    
    async def invalidate_source(self, source: str):
        """إلغاء صحة جميع البيانات من مصدر معين"""
        with self.lock:
            keys_to_remove = self.source_index.get(source, []).copy()
            
            for key in keys_to_remove:
                if key in self.cache:
                    await self._remove_entry(key)
    
    async def cleanup_expired(self):
        """تنظيف البيانات المنتهية الصلاحية"""
        expired_keys = []
        
        with self.lock:
            for key, entry in self.cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
        
        for key in expired_keys:
            await self._remove_entry(key)
        
        logger.info(f"تم تنظيف {len(expired_keys)} مدخل منتهي الصلاحية")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        with self.lock:
            # حساب كفاءة التخزين المؤقت
            if self.stats['total_requests'] > 0:
                self.stats['cache_efficiency'] = (self.stats['hits'] / self.stats['total_requests']) * 100
            
            # حساب استخدام الذاكرة
            self.stats['memory_usage_mb'] = self.current_size_bytes / (1024 * 1024)
            
            return {
                **self.stats,
                'total_entries': len(self.cache),
                'max_size_mb': self.max_size_bytes / (1024 * 1024),
                'entries_by_source': {source: len(keys) for source, keys in self.source_index.items()},
                'entries_by_priority': {priority: len(keys) for priority, keys in self.priority_index.items()},
                'oldest_entry': min([entry.created_at for entry in self.cache.values()]) if self.cache else None,
                'newest_entry': max([entry.created_at for entry in self.cache.values()]) if self.cache else None
            }
    
    def _calculate_size(self, data: Any) -> int:
        """حساب حجم البيانات بالبايت"""
        try:
            if isinstance(data, (str, int, float, bool)):
                return len(str(data).encode('utf-8'))
            elif isinstance(data, (list, dict)):
                return len(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            else:
                return len(pickle.dumps(data))
        except Exception:
            return 1024  # حجم افتراضي
    
    async def _ensure_space(self, required_size: int):
        """التأكد من وجود مساحة كافية"""
        while self.current_size_bytes + required_size > self.max_size_bytes:
            # إزالة أقل البيانات استخداماً (LRU)
            if not self.cache:
                break
            
            # العثور على أقل مدخل استخداماً
            lru_key = next(iter(self.cache))  # أول مدخل (الأقدم)
            await self._remove_entry(lru_key)
            self.stats['evictions'] += 1
    
    async def _remove_entry(self, key: str):
        """إزالة مدخل من التخزين المؤقت"""
        if key not in self.cache:
            return
        
        entry = self.cache[key]
        
        # تحديث الحجم
        self.current_size_bytes -= entry.size_bytes
        
        # إزالة من الفهارس
        if entry.source in self.source_index:
            if key in self.source_index[entry.source]:
                self.source_index[entry.source].remove(key)
        
        if entry.priority in self.priority_index:
            if key in self.priority_index[entry.priority]:
                self.priority_index[entry.priority].remove(key)
        
        # إزالة من التخزين المؤقت
        del self.cache[key]
    
    async def _update_indexes(self, key: str, entry: CacheEntry):
        """تحديث الفهارس"""
        # فهرس المصدر
        if entry.source not in self.source_index:
            self.source_index[entry.source] = []
        self.source_index[entry.source].append(key)
        
        # فهرس الأولوية
        if entry.priority in self.priority_index:
            self.priority_index[entry.priority].append(key)
    
    def _update_response_time(self, response_time_ms: float):
        """تحديث متوسط وقت الاستجابة"""
        current_avg = self.stats['average_response_time']
        total_requests = self.stats['total_requests']
        
        if total_requests > 1:
            self.stats['average_response_time'] = ((current_avg * (total_requests - 1)) + response_time_ms) / total_requests
        else:
            self.stats['average_response_time'] = response_time_ms

    async def _persist_to_db(self, key: str, entry: CacheEntry):
        """حفظ البيانات في قاعدة البيانات للاستمرارية"""
        if not self.db:
            return

        try:
            # تحويل البيانات إلى تنسيق قابل للحفظ
            cache_data = {
                'key': key,
                'data': json.dumps(entry.data, ensure_ascii=False, default=str),
                'created_at': entry.created_at.isoformat(),
                'expires_at': entry.expires_at.isoformat(),
                'priority': entry.priority,
                'source': entry.source,
                'access_count': entry.access_count,
                'size_bytes': entry.size_bytes
            }

            # حفظ في مجموعة cache_entries
            doc_id = hashlib.md5(key.encode()).hexdigest()
            self.db.collection('cache_entries').document(doc_id).set(cache_data, merge=True)

        except Exception as e:
            logger.error(f"خطأ في حفظ البيانات في قاعدة البيانات: {str(e)}")

    async def load_from_db(self, max_age_hours: int = 24):
        """تحميل البيانات من قاعدة البيانات عند بدء التشغيل"""
        if not self.db:
            return

        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

            # جلب البيانات الحديثة من قاعدة البيانات
            query = self.db.collection('cache_entries').where(
                'created_at', '>=', cutoff_time.isoformat()
            )

            docs = query.get()
            loaded_count = 0

            for doc in docs:
                try:
                    data = doc.to_dict()

                    # التحقق من انتهاء الصلاحية
                    expires_at = datetime.fromisoformat(data['expires_at'])
                    if expires_at <= datetime.now():
                        continue

                    # إعادة بناء البيانات
                    entry_data = json.loads(data['data'])

                    # إنشاء مدخل التخزين المؤقت
                    entry = CacheEntry(
                        key=data['key'],
                        data=entry_data,
                        created_at=datetime.fromisoformat(data['created_at']),
                        expires_at=expires_at,
                        priority=data.get('priority', 1),
                        size_bytes=data.get('size_bytes', 0),
                        source=data.get('source', 'unknown'),
                        access_count=data.get('access_count', 0)
                    )

                    # إضافة إلى التخزين المؤقت
                    with self.lock:
                        self.cache[data['key']] = entry
                        self.current_size_bytes += entry.size_bytes
                        await self._update_indexes(data['key'], entry)

                    loaded_count += 1

                except Exception as e:
                    logger.warning(f"خطأ في تحميل مدخل من قاعدة البيانات: {str(e)}")
                    continue

            logger.info(f"تم تحميل {loaded_count} مدخل من قاعدة البيانات")

        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات من قاعدة البيانات: {str(e)}")

    async def start_cleanup_scheduler(self, interval_minutes: int = 30):
        """بدء جدولة تنظيف التخزين المؤقت"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(interval_minutes * 60)
                    await self.cleanup_expired()
                    await self._optimize_cache()
                except Exception as e:
                    logger.error(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")

        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(cleanup_loop())
            logger.info(f"تم بدء جدولة تنظيف التخزين المؤقت كل {interval_minutes} دقيقة")

    async def stop_cleanup_scheduler(self):
        """إيقاف جدولة تنظيف التخزين المؤقت"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            logger.info("تم إيقاف جدولة تنظيف التخزين المؤقت")

    async def _optimize_cache(self):
        """تحسين التخزين المؤقت"""
        with self.lock:
            # إزالة البيانات القديمة والأقل استخداماً
            entries_to_remove = []

            for key, entry in self.cache.items():
                # إزالة البيانات القديمة جداً
                if entry.is_stale(timedelta(hours=2)):
                    entries_to_remove.append(key)
                # إزالة البيانات قليلة الاستخدام
                elif entry.access_count < 2 and entry.priority == 1:
                    age_hours = (datetime.now() - entry.created_at).total_seconds() / 3600
                    if age_hours > 1:  # أقدم من ساعة ولم تُستخدم كثيراً
                        entries_to_remove.append(key)

        # إزالة المدخلات المحددة
        for key in entries_to_remove:
            await self._remove_entry(key)

        if entries_to_remove:
            logger.info(f"تم تحسين التخزين المؤقت بإزالة {len(entries_to_remove)} مدخل")

    async def get_cache_health(self) -> Dict[str, Any]:
        """تقييم صحة التخزين المؤقت"""
        stats = await self.get_cache_stats()

        # تقييم الصحة
        health_score = 100
        issues = []

        # تقييم كفاءة التخزين المؤقت
        if stats['cache_efficiency'] < 50:
            health_score -= 20
            issues.append("كفاءة التخزين المؤقت منخفضة")
        elif stats['cache_efficiency'] < 70:
            health_score -= 10
            issues.append("كفاءة التخزين المؤقت متوسطة")

        # تقييم استخدام الذاكرة
        memory_usage_percent = (stats['memory_usage_mb'] / (self.max_size_bytes / (1024 * 1024))) * 100
        if memory_usage_percent > 90:
            health_score -= 15
            issues.append("استخدام الذاكرة مرتفع جداً")
        elif memory_usage_percent > 75:
            health_score -= 5
            issues.append("استخدام الذاكرة مرتفع")

        # تقييم وقت الاستجابة
        if stats['average_response_time'] > 100:  # أكثر من 100ms
            health_score -= 10
            issues.append("وقت الاستجابة بطيء")

        # تحديد حالة الصحة
        if health_score >= 90:
            health_status = "ممتاز"
        elif health_score >= 75:
            health_status = "جيد"
        elif health_score >= 60:
            health_status = "متوسط"
        else:
            health_status = "ضعيف"

        return {
            'health_score': health_score,
            'health_status': health_status,
            'issues': issues,
            'recommendations': self._get_optimization_recommendations(stats),
            'memory_usage_percent': memory_usage_percent,
            'cache_efficiency': stats['cache_efficiency'],
            'average_response_time': stats['average_response_time']
        }

    def _get_optimization_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """الحصول على توصيات التحسين"""
        recommendations = []

        if stats['cache_efficiency'] < 70:
            recommendations.append("زيادة مدة انتهاء الصلاحية للبيانات المهمة")
            recommendations.append("مراجعة استراتيجية التخزين المؤقت")

        if stats['memory_usage_mb'] > (self.max_size_bytes / (1024 * 1024)) * 0.8:
            recommendations.append("زيادة الحد الأقصى لحجم التخزين المؤقت")
            recommendations.append("تحسين خوارزمية إزالة البيانات")

        if stats['average_response_time'] > 50:
            recommendations.append("تحسين هيكل البيانات المخزنة")
            recommendations.append("استخدام فهرسة أفضل")

        if stats['evictions'] > stats['total_requests'] * 0.1:
            recommendations.append("تقليل حجم البيانات المخزنة")
            recommendations.append("زيادة حجم التخزين المؤقت")

        return recommendations

# إنشاء نسخة عامة من النظام
intelligent_news_cache = None

def initialize_intelligent_news_cache(max_size_mb: int = 100, db=None):
    """تهيئة نظام التخزين المؤقت الذكي"""
    global intelligent_news_cache
    intelligent_news_cache = IntelligentNewsCache(max_size_mb, db)
    logger.info("✅ تم تهيئة نظام التخزين المؤقت الذكي للأخبار")
    return intelligent_news_cache
