"""
وظائف للتحليل المتقدم باستخدام Gemini API
يتضمن تحليلات متقدمة مثل:
- استراتيجيات تداول آلية
- تنبؤات سعرية
- تحليل متعدد الإطارات الزمنية
"""

import logging
import json
import asyncio
import re
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List

# استيراد دوال تنسيق المؤشرات الفنية
from analysis.format_indicators import format_technical_indicators_rtl, format_gemini_analysis

# إعداد السجل
logger = logging.getLogger(__name__)

# سيتم تعريف db لاحقاً بعد استيراد firebase_admin في bot.py
db = None

def set_firestore_db(firestore_db):
    """تعيين قاعدة بيانات Firestore"""
    global db
    db = firestore_db

async def analyze_with_gemini(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]:
    """
    تحليل البيانات باستخدام نموذج Gemini مع دمج التحليل الفني

    Args:
        model: نموذج Gemini
        market_data: بيانات السوق والمؤشرات الفنية
        lang: لغة التحليل (ar أو en)

    Returns:
        نص التحليل أو None إذا فشل التحليل
    """
    # نسخة من البيانات لتجنب تعديل البيانات الأصلية
    market_data = market_data.copy() if market_data else {}
    try:
        if not market_data:
            logger.warning("لا توجد بيانات كافية للتحليل")
            return None

        # استخراج البيانات الأساسية
        symbol = market_data.get('symbol', 'Unknown')
        # تصحيح اسم العملة إذا كان "Unknown"
        if symbol == "Unknown" and "symbol" in market_data:
            symbol = market_data["symbol"]

        # معالجة رمز العملة للتأكد من أنه يعرض العملة الأساسية فقط
        if symbol.endswith('USDT'):
            # استخراج العملة الأساسية (مثل BTC من BTCUSDT)
            base_currency = symbol[:-4]  # إزالة "USDT" من نهاية الرمز
            logger.info(f"تم استخراج العملة الأساسية: {base_currency} من الرمز: {symbol}")
            symbol = base_currency  # استخدام العملة الأساسية فقط في التحليل

        # التحقق من وجود بيانات السعر
        price = market_data.get('price', 0)
        if price == 0 and 'current_price' in market_data:
            price = market_data.get('current_price', 0)
            logger.info(f"تم استخدام current_price بدلاً من price: {price}")

        # التأكد من أن السعر رقم
        if isinstance(price, str):
            try:
                price = float(price)
                logger.info(f"تم تحويل السعر من نص إلى رقم: {price}")
            except ValueError:
                logger.warning(f"لا يمكن تحويل السعر إلى رقم: {price}")
                price = 0

        price_change = market_data.get('price_change', 0)
        if price_change == 0 and 'price_change_percent_24h' in market_data:
            price_change = market_data.get('price_change_percent_24h', 0)
            logger.info(f"تم استخدام price_change_percent_24h بدلاً من price_change: {price_change}")

        # التأكد من أن نسبة التغيير رقم
        if isinstance(price_change, str):
            try:
                price_change = float(price_change)
                logger.info(f"تم تحويل نسبة التغيير من نص إلى رقم: {price_change}")
            except ValueError:
                logger.warning(f"لا يمكن تحويل نسبة التغيير إلى رقم: {price_change}")
                price_change = 0

        # سجل البيانات للتشخيص
        logger.info(f"بيانات التحليل: العملة={symbol}, السعر={price}, التغير={price_change}%")

        # استخراج المؤشرات الفنية
        indicators = {
            'RSI': market_data.get('rsi'),
            'EMA20': market_data.get('ema20'),
            'EMA50': market_data.get('ema50'),
            'MACD': market_data.get('macd'),
            'MACD Signal': market_data.get('macd_signal'),
            'MACD Histogram': market_data.get('macd_histogram'),
            'Bollinger Upper': market_data.get('bb_upper'),
            'Bollinger Middle': market_data.get('bb_middle'),
            'Bollinger Lower': market_data.get('bb_lower'),
            'Stochastic K': market_data.get('stoch_k'),
            'Stochastic D': market_data.get('stoch_d'),
            'ADX': market_data.get('adx'),
            'Plus DI': market_data.get('plus_di'),
            'Minus DI': market_data.get('minus_di'),
            # إضافة مؤشر Ichimoku Cloud
            'Ichimoku Tenkan': market_data.get('ichimoku_tenkan'),
            'Ichimoku Kijun': market_data.get('ichimoku_kijun'),
            'Ichimoku Senkou A': market_data.get('ichimoku_senkou_a'),
            'Ichimoku Senkou B': market_data.get('ichimoku_senkou_b'),
            'Ichimoku Chikou': market_data.get('ichimoku_chikou')
        }

        # استخراج إشارات المؤشرات النصية إذا كانت متاحة
        indicator_signals = {
            'RSI': market_data.get('rsi_signal_text', 'neutral'),
            'EMA': market_data.get('ema_signal_text', 'neutral'),
            'MACD': market_data.get('macd_signal_text', 'neutral'),
            'Bollinger Bands': market_data.get('bb_signal_text', 'neutral'),
            'Stochastic': market_data.get('stoch_signal_text', 'neutral'),
            'ADX': market_data.get('adx_signal_text', 'neutral')
        }

        # استخراج التوصية النصية إذا كانت متاحة
        recommendation = market_data.get('recommendation_text', 'hold')

        # تصفية المؤشرات ذات القيم الصالحة
        valid_indicators = {k: v for k, v in indicators.items() if v not in [None, 'nan', 'N/A']}

        # تحليل المؤشرات الفنية مسبقاً
        technical_analysis = {}

        # استخدام إشارات المؤشرات النصية إذا كانت متاحة
        if 'rsi_signal_text' in market_data:
            rsi_signal = market_data.get('rsi_signal_text')
            if rsi_signal == 'oversold':
                technical_analysis['RSI'] = "تشبع بيع" if lang == 'ar' else "Oversold"
            elif rsi_signal == 'overbought':
                technical_analysis['RSI'] = "تشبع شراء" if lang == 'ar' else "Overbought"
            else:
                technical_analysis['RSI'] = "محايد" if lang == 'ar' else "Neutral"

            ema_signal = market_data.get('ema_signal_text')
            if ema_signal == 'bullish':
                technical_analysis['EMA'] = "اتجاه صعودي" if lang == 'ar' else "Upward Trend"
            else:
                technical_analysis['EMA'] = "اتجاه هبوطي" if lang == 'ar' else "Downward Trend"

            macd_signal = market_data.get('macd_signal_text')
            if macd_signal == 'buy':
                technical_analysis['MACD'] = "إشارة شراء" if lang == 'ar' else "Bullish Signal"
            else:
                technical_analysis['MACD'] = "إشارة بيع" if lang == 'ar' else "Bearish Signal"
        else:
            # تحليل RSI
            rsi_value = market_data.get('rsi')
            if rsi_value not in ['nan', 'N/A', None]:
                try:
                    rsi_value = float(rsi_value)
                    if rsi_value > 70:
                        technical_analysis['RSI'] = "تشبع شراء" if lang == 'ar' else "Overbought"
                    elif rsi_value < 30:
                        technical_analysis['RSI'] = "تشبع بيع" if lang == 'ar' else "Oversold"
                    else:
                        technical_analysis['RSI'] = "محايد" if lang == 'ar' else "Neutral"
                except (ValueError, TypeError):
                    pass

            # تحليل EMA
            ema20 = market_data.get('ema20')
            ema50 = market_data.get('ema50')
            if ema20 not in ['nan', 'N/A', None] and ema50 not in ['nan', 'N/A', None]:
                try:
                    ema20 = float(ema20)
                    ema50 = float(ema50)
                    if ema20 > ema50:
                        technical_analysis['EMA'] = "اتجاه صعودي" if lang == 'ar' else "Upward Trend"
                    else:
                        technical_analysis['EMA'] = "اتجاه هبوطي" if lang == 'ar' else "Downward Trend"
                except (ValueError, TypeError):
                    pass

            # تحليل MACD
            macd = market_data.get('macd')
            macd_signal = market_data.get('macd_signal')
            if macd not in ['nan', 'N/A', None] and macd_signal not in ['nan', 'N/A', None]:
                try:
                    macd = float(macd)
                    macd_signal = float(macd_signal)
                    macd_diff = macd - macd_signal
                    if macd_diff > 0:
                        technical_analysis['MACD'] = "إشارة شراء" if lang == 'ar' else "Bullish Signal"
                    else:
                        technical_analysis['MACD'] = "إشارة بيع" if lang == 'ar' else "Bearish Signal"
                except (ValueError, TypeError):
                    pass

        # تحليل Bollinger Bands
        bb_upper = market_data.get('bb_upper')
        bb_middle = market_data.get('bb_middle')
        bb_lower = market_data.get('bb_lower')
        if all(x not in ['nan', 'N/A', None] for x in [bb_upper, bb_middle, bb_lower]):
            try:
                bb_upper = float(bb_upper)
                bb_middle = float(bb_middle)
                bb_lower = float(bb_lower)
                price_val = float(price)
                if price_val > bb_upper:
                    technical_analysis['Bollinger Bands'] = "منطقة تشبع شراء" if lang == 'ar' else "Overbought Area"
                elif price_val < bb_lower:
                    technical_analysis['Bollinger Bands'] = "منطقة تشبع بيع" if lang == 'ar' else "Oversold Area"
                else:
                    technical_analysis['Bollinger Bands'] = "نطاق تداول طبيعي" if lang == 'ar' else "Normal Trading Range"
            except (ValueError, TypeError):
                pass

        # تحليل Ichimoku Cloud
        ichimoku_tenkan = market_data.get('ichimoku_tenkan')
        ichimoku_kijun = market_data.get('ichimoku_kijun')
        ichimoku_senkou_a = market_data.get('ichimoku_senkou_a')
        ichimoku_senkou_b = market_data.get('ichimoku_senkou_b')

        if all(x not in ['nan', 'N/A', None] for x in [ichimoku_tenkan, ichimoku_kijun]):
            try:
                ichimoku_tenkan = float(ichimoku_tenkan)
                ichimoku_kijun = float(ichimoku_kijun)
                price_val = float(price)

                # تحليل إشارة Ichimoku Cloud
                if ichimoku_tenkan > ichimoku_kijun:
                    technical_analysis['Ichimoku Cloud'] = "إشارة صعودية (خط التحويل فوق خط الأساس)" if lang == 'ar' else "Bullish Signal (Tenkan-sen above Kijun-sen)"
                elif ichimoku_tenkan < ichimoku_kijun:
                    technical_analysis['Ichimoku Cloud'] = "إشارة هبوطية (خط التحويل تحت خط الأساس)" if lang == 'ar' else "Bearish Signal (Tenkan-sen below Kijun-sen)"
                else:
                    technical_analysis['Ichimoku Cloud'] = "إشارة محايدة" if lang == 'ar' else "Neutral Signal"

                # تحليل موقع السعر بالنسبة للسحابة
                if ichimoku_senkou_a not in ['nan', 'N/A', None] and ichimoku_senkou_b not in ['nan', 'N/A', None]:
                    ichimoku_senkou_a = float(ichimoku_senkou_a)
                    ichimoku_senkou_b = float(ichimoku_senkou_b)

                    cloud_top = max(ichimoku_senkou_a, ichimoku_senkou_b)
                    cloud_bottom = min(ichimoku_senkou_a, ichimoku_senkou_b)

                    if price_val > cloud_top:
                        technical_analysis['Ichimoku Position'] = "فوق السحابة (اتجاه صعودي قوي)" if lang == 'ar' else "Above the Cloud (Strong Bullish)"
                    elif price_val < cloud_bottom:
                        technical_analysis['Ichimoku Position'] = "تحت السحابة (اتجاه هبوطي قوي)" if lang == 'ar' else "Below the Cloud (Strong Bearish)"
                    else:
                        technical_analysis['Ichimoku Position'] = "داخل السحابة (اتجاه غير واضح)" if lang == 'ar' else "Inside the Cloud (Uncertain Trend)"
            except (ValueError, TypeError):
                pass

        # تحليل Stochastic RSI
        stoch_k = market_data.get('stoch_k')
        stoch_d = market_data.get('stoch_d')
        if stoch_k not in ['nan', 'N/A', None] and stoch_d not in ['nan', 'N/A', None]:
            try:
                stoch_k = float(stoch_k)
                stoch_d = float(stoch_d)
                if stoch_k > 80 and stoch_d > 80:
                    technical_analysis['Stochastic RSI'] = "تشبع شراء" if lang == 'ar' else "Overbought"
                elif stoch_k < 20 and stoch_d < 20:
                    technical_analysis['Stochastic RSI'] = "تشبع بيع" if lang == 'ar' else "Oversold"
                else:
                    technical_analysis['Stochastic RSI'] = "محايد" if lang == 'ar' else "Neutral"
            except (ValueError, TypeError):
                pass

        # تحديد التوصية العامة بناءً على التحليل الفني
        buy_signals = 0
        sell_signals = 0
        neutral_signals = 0

        for indicator, analysis in technical_analysis.items():
            if analysis in ["تشبع بيع", "إشارة شراء", "Oversold", "Bullish Signal", "إشارة صعودية (خط التحويل فوق خط الأساس)", "فوق السحابة (اتجاه صعودي قوي)", "Above the Cloud (Strong Bullish)", "Bullish Signal (Tenkan-sen above Kijun-sen)"]:
                buy_signals += 1
            elif analysis in ["تشبع شراء", "إشارة بيع", "Overbought", "Bearish Signal", "إشارة هبوطية (خط التحويل تحت خط الأساس)", "تحت السحابة (اتجاه هبوطي قوي)", "Below the Cloud (Strong Bearish)", "Bearish Signal (Tenkan-sen below Kijun-sen)"]:
                sell_signals += 1
            else:
                neutral_signals += 1

        if buy_signals > sell_signals:
            recommendation = "شراء" if lang == 'ar' else "Buy"
        elif sell_signals > buy_signals:
            recommendation = "بيع" if lang == 'ar' else "Sell"
        else:
            recommendation = "انتظار" if lang == 'ar' else "Hold"

        # إنشاء نص التحليل الفني - تحسين العرض وإضافة المزيد من التفاصيل
        technical_analysis_text = ""
        if lang == 'ar':
            # تنظيم المؤشرات في مجموعات منطقية
            technical_analysis_text = "🔍 المؤشرات الفنية الرئيسية:\n"

            # مؤشرات الاتجاه
            technical_analysis_text += "\n📈 مؤشرات الاتجاه:\n"
            if 'EMA20' in valid_indicators:
                technical_analysis_text += f"• المتوسط المتحرك الأسي (EMA20): {valid_indicators.get('EMA20'):.2f}\n"
            if 'EMA50' in valid_indicators:
                technical_analysis_text += f"• المتوسط المتحرك الأسي (EMA50): {valid_indicators.get('EMA50'):.2f}\n"
            if 'EMA' in technical_analysis:
                technical_analysis_text += f"  ↳ التفسير: {technical_analysis.get('EMA')}\n"

            # مؤشرات الزخم
            technical_analysis_text += "\n🔋 مؤشرات الزخم:\n"
            if 'RSI' in valid_indicators:
                technical_analysis_text += f"• مؤشر القوة النسبية (RSI): {valid_indicators.get('RSI'):.2f}\n"
                technical_analysis_text += f"  ↳ التفسير: {technical_analysis.get('RSI', 'محايد')}\n"

            if 'MACD' in valid_indicators:
                technical_analysis_text += f"• مؤشر تقارب وتباعد المتوسطات المتحركة (MACD): {valid_indicators.get('MACD'):.4f}\n"
                technical_analysis_text += f"• خط الإشارة: {valid_indicators.get('MACD Signal'):.4f}\n"
                technical_analysis_text += f"  ↳ التفسير: {technical_analysis.get('MACD', 'محايد')}\n"

            # مؤشرات التذبذب
            technical_analysis_text += "\n🔄 مؤشرات التذبذب:\n"
            if 'Bollinger Upper' in valid_indicators:
                technical_analysis_text += f"• نطاق بولينجر العلوي: {valid_indicators.get('Bollinger Upper'):.2f}\n"
                technical_analysis_text += f"• نطاق بولينجر المتوسط: {valid_indicators.get('Bollinger Middle'):.2f}\n"
                technical_analysis_text += f"• نطاق بولينجر السفلي: {valid_indicators.get('Bollinger Lower'):.2f}\n"
                technical_analysis_text += f"  ↳ التفسير: {technical_analysis.get('Bollinger Bands', 'نطاق تداول طبيعي')}\n"

            # مؤشر Ichimoku Cloud
            if 'Ichimoku Tenkan' in valid_indicators and 'Ichimoku Kijun' in valid_indicators:
                technical_analysis_text += "\n☁️ مؤشر سحابة إيشيموكو (Ichimoku Cloud):\n"
                technical_analysis_text += f"• خط التحويل (Tenkan-sen): {valid_indicators.get('Ichimoku Tenkan'):.4f}\n"
                technical_analysis_text += f"• خط الأساس (Kijun-sen): {valid_indicators.get('Ichimoku Kijun'):.4f}\n"
                if 'Ichimoku Senkou A' in valid_indicators and 'Ichimoku Senkou B' in valid_indicators:
                    technical_analysis_text += f"• خط السبان A (Senkou Span A): {valid_indicators.get('Ichimoku Senkou A'):.4f}\n"
                    technical_analysis_text += f"• خط السبان B (Senkou Span B): {valid_indicators.get('Ichimoku Senkou B'):.4f}\n"
                if 'Ichimoku Cloud' in technical_analysis:
                    technical_analysis_text += f"  ↳ التفسير: {technical_analysis.get('Ichimoku Cloud')}\n"
                if 'Ichimoku Position' in technical_analysis:
                    technical_analysis_text += f"  ↳ موقع السعر: {technical_analysis.get('Ichimoku Position')}\n"

            # التوصية النهائية بشكل بارز
            technical_analysis_text += f"\n⭐️ التوصية النهائية: {recommendation} ⭐️\n"
            technical_analysis_text += f"(بناءً على {buy_signals} إشارات شراء و {sell_signals} إشارات بيع و {neutral_signals} إشارات محايدة)\n"
        else:
            # تنظيم المؤشرات في مجموعات منطقية باللغة الإنجليزية
            technical_analysis_text = "🔍 Key Technical Indicators:\n"

            # مؤشرات الاتجاه
            technical_analysis_text += "\n📈 Trend Indicators:\n"
            if 'EMA20' in valid_indicators:
                technical_analysis_text += f"• Exponential Moving Average (EMA20): {valid_indicators.get('EMA20'):.2f}\n"
            if 'EMA50' in valid_indicators:
                technical_analysis_text += f"• Exponential Moving Average (EMA50): {valid_indicators.get('EMA50'):.2f}\n"
            if 'EMA' in technical_analysis:
                technical_analysis_text += f"  ↳ Interpretation: {technical_analysis.get('EMA')}\n"

            # مؤشرات الزخم
            technical_analysis_text += "\n🔋 Momentum Indicators:\n"
            if 'RSI' in valid_indicators:
                technical_analysis_text += f"• Relative Strength Index (RSI): {valid_indicators.get('RSI'):.2f}\n"
                technical_analysis_text += f"  ↳ Interpretation: {technical_analysis.get('RSI', 'Neutral')}\n"

            if 'MACD' in valid_indicators:
                technical_analysis_text += f"• Moving Average Convergence Divergence (MACD): {valid_indicators.get('MACD'):.4f}\n"
                technical_analysis_text += f"• Signal Line: {valid_indicators.get('MACD Signal'):.4f}\n"
                technical_analysis_text += f"  ↳ Interpretation: {technical_analysis.get('MACD', 'Neutral')}\n"

            # مؤشرات التذبذب
            technical_analysis_text += "\n🔄 Volatility Indicators:\n"
            if 'Bollinger Upper' in valid_indicators:
                technical_analysis_text += f"• Bollinger Upper Band: {valid_indicators.get('Bollinger Upper'):.2f}\n"
                technical_analysis_text += f"• Bollinger Middle Band: {valid_indicators.get('Bollinger Middle'):.2f}\n"
                technical_analysis_text += f"• Bollinger Lower Band: {valid_indicators.get('Bollinger Lower'):.2f}\n"
                technical_analysis_text += f"  ↳ Interpretation: {technical_analysis.get('Bollinger Bands', 'Normal Trading Range')}\n"

            # مؤشر Ichimoku Cloud
            if 'Ichimoku Tenkan' in valid_indicators and 'Ichimoku Kijun' in valid_indicators:
                technical_analysis_text += "\n☁️ Ichimoku Cloud Indicator:\n"
                technical_analysis_text += f"• Tenkan-sen (Conversion Line): {valid_indicators.get('Ichimoku Tenkan'):.4f}\n"
                technical_analysis_text += f"• Kijun-sen (Base Line): {valid_indicators.get('Ichimoku Kijun'):.4f}\n"
                if 'Ichimoku Senkou A' in valid_indicators and 'Ichimoku Senkou B' in valid_indicators:
                    technical_analysis_text += f"• Senkou Span A: {valid_indicators.get('Ichimoku Senkou A'):.4f}\n"
                    technical_analysis_text += f"• Senkou Span B: {valid_indicators.get('Ichimoku Senkou B'):.4f}\n"
                if 'Ichimoku Cloud' in technical_analysis:
                    technical_analysis_text += f"  ↳ Interpretation: {technical_analysis.get('Ichimoku Cloud')}\n"
                if 'Ichimoku Position' in technical_analysis:
                    technical_analysis_text += f"  ↳ Price Position: {technical_analysis.get('Ichimoku Position')}\n"

            # التوصية النهائية بشكل بارز
            technical_analysis_text += f"\n⭐️ Final Recommendation: {recommendation} ⭐️\n"
            technical_analysis_text += f"(Based on {buy_signals} buy signals, {sell_signals} sell signals, and {neutral_signals} neutral signals)\n"

        # إنشاء سياق للنموذج حسب اللغة مع تضمين التحليل الفني
        if lang == 'ar':
            prompt = f"""
            قم بتحليل البيانات التالية لعملة {symbol} وقدم تحليلاً فنياً مفصلاً باللغة العربية:

            السعر الحالي: {price}
            نسبة التغير: {price_change}%

            المؤشرات الفنية وتفسيرها:
            {technical_analysis_text}

            يجب أن يتضمن التحليل:
            1. تقييم الاتجاه العام (صعودي، هبوطي، أو محايد)
            2. مستويات الدعم والمقاومة المحتملة (أعط أرقاماً محددة)
            3. تحديد السيناريوهات السعرية المحتملة (صعودي، هبوطي، جانبي) مع تحديد المستويات السعرية لكل سيناريو
            4. تحليل مؤشر سحابة إيشيموكو (Ichimoku Cloud) وتفسير إشاراته
            5. اقتراح استراتيجيات تداول محددة مع نقاط دخول وخروج وأوامر وقف الخسارة
            6. توصية واضحة وصريحة (شراء، بيع، أو انتظار) مع تبرير التوصية

            قواعد التنسيق والكتابة:
            - استخدم اللغة العربية الفصحى أو اللهجة السعودية
            - اكتب بوضوح مع مسافات مناسبة بين الكلمات العربية
            - تأكد من عدم دمج الكلمات (مثل: اكتب "مقدمة في" وليس "مقدمةفي")
            - استخدم الرموز التعبيرية المتنوعة للأقسام المختلفة
            - استخدم النص العريض للعناوين المهمة بدون نقطتين داخل النص العريض
            - قدم أرقاماً محددة لجميع المستويات السعرية
            - نظم التحليل في أقسام واضحة ومنفصلة
            - لا تذكر أنك نموذج ذكاء اصطناعي
            - لا تضع تنبيهات أو إخلاء مسؤولية في نهاية التحليل

            هيكل التحليل المطلوب:
            📈 **الاتجاه العام** - تقييم الاتجاه وقوته
            🎯 **مستويات الدعم والمقاومة** - أرقام محددة
            🔮 **السيناريوهات السعرية** - سيناريوهات مع مستويات سعرية
            ⚡ **استراتيجية التداول** - نقاط دخول وخروج محددة
            💡 **التوصية النهائية** - توصية واضحة مع التبرير
            """
        else:
            prompt = f"""
            Analyze the following data for {symbol} and provide a detailed technical analysis in English:

            Current Price: {price}
            Price Change: {price_change}%

            Technical Indicators and Interpretation:
            {technical_analysis_text}

            The analysis should include:
            1. Overall trend assessment (bullish, bearish, or neutral)
            2. Potential support and resistance levels (give specific numbers)
            3. Identify potential price scenarios (bullish, bearish, sideways) with specific price levels for each scenario
            4. Analysis of the Ichimoku Cloud indicator and interpretation of its signals
            5. Suggest specific trading strategies with entry/exit points and stop-loss orders
            6. Clear and explicit recommendation (buy, sell, or hold) with justification

            Important rules:
            - Provide the analysis in an easy-to-read format with appropriate emojis
            - Make the recommendation very clear and put it in a separate paragraph with the heading "⭐️ **Recommendation**" (without colon)
            - Specify support and resistance levels with precise numbers (example: **Support level** 0.2500, **Resistance level** 0.2700)
            - Do not put colons inside bold text, either place them outside or don't use them
            - Do not add any disclaimers or notes about the analysis being for educational purposes only
            - Do not add any disclaimers at the end of the analysis
            - Focus on providing detailed and useful analysis while maintaining important details
            - Provide specific price scenarios with numbers, not just general statements
            - Suggest a specific trading strategy with entry/exit points and stop-loss orders with numbers
            """

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            logger.info("محاولة استدعاء Gemini باستخدام to_thread")
            try:
                # تحويل القيم الرقمية في prompt إلى نصوص لتجنب أخطاء التنسيق
                safe_prompt = prompt
                if isinstance(price, (int, float)):
                    safe_prompt = safe_prompt.replace(f"السعر الحالي: {price}", f"السعر الحالي: {str(price)}")
                    safe_prompt = safe_prompt.replace(f"Current Price: {price}", f"Current Price: {str(price)}")

                if isinstance(price_change, (int, float)):
                    safe_prompt = safe_prompt.replace(f"نسبة التغير: {price_change}%", f"نسبة التغير: {str(price_change)}%")
                    safe_prompt = safe_prompt.replace(f"Price Change: {price_change}%", f"Price Change: {str(price_change)}%")

                response_obj = await asyncio.to_thread(
                    lambda: model.generate_content(safe_prompt)
                )
                response = response_obj.text
                logger.info(f"تم الحصول على استجابة من Gemini باستخدام to_thread بطول {len(response)} حرف")

                # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
                min_response_length = 100  # الحد الأدنى المقبول لطول الرد
                if len(response) < min_response_length:
                    logger.warning(f"استجابة Gemini قصيرة جدًا ({len(response)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

                    # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
                    enhanced_prompt = safe_prompt + "\n\n"
                    if lang == 'ar':
                        enhanced_prompt += """
يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:
1. تحليلاً مفصلاً للوضع الحالي
2. توقعات مستقبلية مع أرقام محددة
3. استراتيجيات واضحة للتداول
4. توصيات محددة بناءً على التحليل الفني
5. شرح للمخاطر المحتملة
6. استخدم الايموجي للتعبير
يجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.
"""
                    else:
                        enhanced_prompt += """
Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:
1. Detailed analysis of the current situation
2. Future predictions with specific numbers
3. Clear trading strategies
4. Specific recommendations based on technical analysis
5. Explanation of potential risks
6. Use emojis for clarity
The answer should be comprehensive and cover all aspects of the question in depth.
"""

                    # إعادة استدعاء النموذج مع التوجيهات المحسنة
                    logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
                    try:
                        response_obj = await asyncio.to_thread(
                            lambda: model.generate_content(enhanced_prompt)
                        )
                        response = response_obj.text
                        logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(response)} حرف")
                    except Exception as retry_error:
                        logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                        # نستمر باستخدام الاستجابة الأصلية
            except Exception as thread_error:
                logger.warning(f"فشل استدعاء to_thread: {str(thread_error)}")

                # محاولة استخدام generate_content_async
                logger.info("محاولة استدعاء Gemini باستخدام generate_content_async")
                try:
                    # تحويل القيم الرقمية في prompt إلى نصوص لتجنب أخطاء التنسيق
                    safe_prompt = prompt
                    if isinstance(price, (int, float)):
                        safe_prompt = safe_prompt.replace(f"السعر الحالي: {price}", f"السعر الحالي: {str(price)}")
                        safe_prompt = safe_prompt.replace(f"Current Price: {price}", f"Current Price: {str(price)}")

                    if isinstance(price_change, (int, float)):
                        safe_prompt = safe_prompt.replace(f"نسبة التغير: {price_change}%", f"نسبة التغير: {str(price_change)}%")
                        safe_prompt = safe_prompt.replace(f"Price Change: {price_change}%", f"Price Change: {str(price_change)}%")

                    response_obj = await model.generate_content_async(safe_prompt)
                    if hasattr(response_obj, 'text'):
                        response = response_obj.text
                    else:
                        response = str(response_obj)
                    logger.info(f"تم الحصول على استجابة من Gemini باستخدام generate_content_async بطول {len(response)} حرف")

                    # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
                    min_response_length = 100  # الحد الأدنى المقبول لطول الرد
                    if len(response) < min_response_length:
                        logger.warning(f"استجابة Gemini قصيرة جدًا ({len(response)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

                        # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
                        enhanced_prompt = safe_prompt + "\n\n"
                        if lang == 'ar':
                            enhanced_prompt += """
يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:
1. تحليلاً مفصلاً للوضع الحالي
2. توقعات مستقبلية مع أرقام محددة
3. استراتيجيات واضحة للتداول
4. توصيات محددة بناءً على التحليل الفني
5. شرح للمخاطر المحتملة

يجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.
"""
                        else:
                            enhanced_prompt += """
Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:
1. Detailed analysis of the current situation
2. Future predictions with specific numbers
3. Clear trading strategies
4. Specific recommendations based on technical analysis
5. Explanation of potential risks

The answer should be comprehensive and cover all aspects of the question in depth.
"""

                        # إعادة استدعاء النموذج مع التوجيهات المحسنة
                        logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
                        try:
                            response_obj = await model.generate_content_async(enhanced_prompt)
                            if hasattr(response_obj, 'text'):
                                response = response_obj.text
                            else:
                                response = str(response_obj)
                            logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(response)} حرف")
                        except Exception as retry_error:
                            logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                            # نستمر باستخدام الاستجابة الأصلية
                except Exception as async_error:
                    logger.error(f"فشل استدعاء generate_content_async: {str(async_error)}")

                    # محاولة أخيرة باستخدام generate_content مباشرة
                    logger.info("محاولة استدعاء Gemini باستخدام generate_content مباشرة")
                    try:
                        # تحويل القيم الرقمية في prompt إلى نصوص لتجنب أخطاء التنسيق
                        safe_prompt = prompt
                        if isinstance(price, (int, float)):
                            safe_prompt = safe_prompt.replace(f"السعر الحالي: {price}", f"السعر الحالي: {str(price)}")
                            safe_prompt = safe_prompt.replace(f"Current Price: {price}", f"Current Price: {str(price)}")

                        if isinstance(price_change, (int, float)):
                            safe_prompt = safe_prompt.replace(f"نسبة التغير: {price_change}%", f"نسبة التغير: {str(price_change)}%")
                            safe_prompt = safe_prompt.replace(f"Price Change: {price_change}%", f"Price Change: {str(price_change)}%")

                        response_obj = model.generate_content(safe_prompt)
                        response = response_obj.text
                        logger.info(f"تم الحصول على استجابة من Gemini باستخدام generate_content مباشرة بطول {len(response)} حرف")

                        # التحقق من طول الرد - إذا كان قصيرًا جدًا، نعيد المحاولة
                        min_response_length = 100  # الحد الأدنى المقبول لطول الرد
                        if len(response) < min_response_length:
                            logger.warning(f"استجابة Gemini قصيرة جدًا ({len(response)} حرف). محاولة إعادة الاستدعاء مع توجيهات إضافية.")

                            # إضافة توجيهات إضافية للحصول على رد أكثر تفصيلاً
                            enhanced_prompt = safe_prompt + "\n\n"
                            if lang == 'ar':
                                enhanced_prompt += """
يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:
1. تحليلاً مفصلاً للوضع الحالي
2. توقعات مستقبلية مع أرقام محددة
3. استراتيجيات واضحة للتداول
4. توصيات محددة بناءً على التحليل الفني
5. شرح للمخاطر المحتملة

يجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.
"""
                            else:
                                enhanced_prompt += """
Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:
1. Detailed analysis of the current situation
2. Future predictions with specific numbers
3. Clear trading strategies
4. Specific recommendations based on technical analysis
5. Explanation of potential risks

The answer should be comprehensive and cover all aspects of the question in depth.
"""

                            # إعادة استدعاء النموذج مع التوجيهات المحسنة
                            logger.info("إعادة استدعاء نموذج Gemini مع توجيهات إضافية")
                            try:
                                response_obj = model.generate_content(enhanced_prompt)
                                response = response_obj.text
                                logger.info(f"تم الحصول على استجابة محسنة من Gemini بطول {len(response)} حرف")
                            except Exception as retry_error:
                                logger.error(f"فشل في إعادة استدعاء Gemini API: {str(retry_error)}")
                                # نستمر باستخدام الاستجابة الأصلية
                    except Exception as direct_error:
                        logger.error(f"فشل استدعاء generate_content مباشرة: {str(direct_error)}")
                        raise Exception("فشلت جميع محاولات استدعاء Gemini API")
        except Exception as api_error:
            logger.error(f"خطأ في استدعاء Gemini API: {str(api_error)}")
            # إذا فشل Gemini، استخدم التحليل الفني المحسن
            if lang == 'ar':
                response = f"""🔎 تحليل فني مفصل:

📈 تحليل الاتجاه:
• {technical_analysis.get('EMA', 'الاتجاه العام غير محدد')}
• المتوسط المتحرك الأسي (EMA20): {valid_indicators.get('EMA20', 'غير متوفر')}
• المتوسط المتحرك الأسي (EMA50): {valid_indicators.get('EMA50', 'غير متوفر')}

🔋 تحليل الزخم:
• مؤشر القوة النسبية (RSI): {valid_indicators.get('RSI', 'غير متوفر')} - {technical_analysis.get('RSI', 'حالة متوازنة')}
• مؤشر MACD: {valid_indicators.get('MACD', 'غير متوفر')} - {technical_analysis.get('MACD', 'إشارة محايدة')}

🔄 تحليل التذبذب:
• نطاقات بولينجر: {technical_analysis.get('Bollinger Bands', 'نطاق تداول طبيعي')}

⭐️ التوصية النهائية: {recommendation} ⭐️
(بناءً على {buy_signals} إشارات شراء و {sell_signals} إشارات بيع و {neutral_signals} إشارات محايدة)

"""
            else:
                response = f"""🔎 Detailed Technical Analysis:

📈 Trend Analysis:
• {technical_analysis.get('EMA', 'General trend is undefined')}
• Exponential Moving Average (EMA20): {valid_indicators.get('EMA20', 'Not available')}
• Exponential Moving Average (EMA50): {valid_indicators.get('EMA50', 'Not available')}

🔋 Momentum Analysis:
• Relative Strength Index (RSI): {valid_indicators.get('RSI', 'Not available')} - {technical_analysis.get('RSI', 'Balanced condition')}
• MACD Indicator: {valid_indicators.get('MACD', 'Not available')} - {technical_analysis.get('MACD', 'Neutral signal')}

🔄 Volatility Analysis:
• Bollinger Bands: {technical_analysis.get('Bollinger Bands', 'Normal trading range')}

⭐️ Final Recommendation: {recommendation} ⭐️
(Based on {buy_signals} buy signals, {sell_signals} sell signals, and {neutral_signals} neutral signals)

"""

        if not response:
            logger.warning("لم يتم الحصول على استجابة من Gemini API")
            # استخدام التحليل الفني المحسن
            if lang == 'ar':
                response = f"""🔎 تحليل فني مفصل:

📈 تحليل الاتجاه:
• {technical_analysis.get('EMA', 'الاتجاه العام غير محدد')}
• المتوسط المتحرك الأسي (EMA20): {valid_indicators.get('EMA20', 'غير متوفر')}
• المتوسط المتحرك الأسي (EMA50): {valid_indicators.get('EMA50', 'غير متوفر')}

🔋 تحليل الزخم:
• مؤشر القوة النسبية (RSI): {valid_indicators.get('RSI', 'غير متوفر')} - {technical_analysis.get('RSI', 'حالة متوازنة')}
• مؤشر MACD: {valid_indicators.get('MACD', 'غير متوفر')} - {technical_analysis.get('MACD', 'إشارة محايدة')}

🔄 تحليل التذبذب:
• نطاقات بولينجر: {technical_analysis.get('Bollinger Bands', 'نطاق تداول طبيعي')}

⭐️ التوصية النهائية: {recommendation} ⭐️
(بناءً على {buy_signals} إشارات شراء و {sell_signals} إشارات بيع و {neutral_signals} إشارات محايدة)

ملاحظة: هذا التحليل مبني على المؤشرات الفنية المتاحة فقط ويجب إجراء المزيد من البحث قبل اتخاذ أي قرار استثماري."""
            else:
                response = f"""🔎 Detailed Technical Analysis:

📈 Trend Analysis:
• {technical_analysis.get('EMA', 'General trend is undefined')}
• Exponential Moving Average (EMA20): {valid_indicators.get('EMA20', 'Not available')}
• Exponential Moving Average (EMA50): {valid_indicators.get('EMA50', 'Not available')}

🔋 Momentum Analysis:
• Relative Strength Index (RSI): {valid_indicators.get('RSI', 'Not available')} - {technical_analysis.get('RSI', 'Balanced condition')}
• MACD Indicator: {valid_indicators.get('MACD', 'Not available')} - {technical_analysis.get('MACD', 'Neutral signal')}

🔄 Volatility Analysis:
• Bollinger Bands: {technical_analysis.get('Bollinger Bands', 'Normal trading range')}

⭐️ Final Recommendation: {recommendation} ⭐️
(Based on {buy_signals} buy signals, {sell_signals} sell signals, and {neutral_signals} neutral signals)

Note: This analysis is based on available technical indicators only and further research should be conducted before making any investment decisions."""

        # تنسيق النتيجة بطريقة جديدة تماماً
        # تحديد التوصية النهائية بناءً على التحليل الفني
        if buy_signals > sell_signals:
            recommendation_emoji = "🟢" if lang == 'ar' else "🟢"
            recommendation_text = "شراء" if lang == 'ar' else "BUY"
        elif sell_signals > buy_signals:
            recommendation_emoji = "🔴" if lang == 'ar' else "🔴"
            recommendation_text = "بيع" if lang == 'ar' else "SELL"
        else:
            recommendation_emoji = "🟡" if lang == 'ar' else "🟡"
            recommendation_text = "انتظار" if lang == 'ar' else "HOLD"

        # تحديد اتجاه السعر
        if price_change > 0:
            price_trend_emoji = "📈"
        elif price_change < 0:
            price_trend_emoji = "📉"
        else:
            price_trend_emoji = "➡️"

        # تحديد قوة الإشارة
        signal_strength = max(buy_signals, sell_signals) - min(buy_signals, sell_signals)
        if signal_strength >= 3:
            signal_strength_text = "قوية جداً" if lang == 'ar' else "Very Strong"
            signal_stars = "⭐⭐⭐⭐⭐"
        elif signal_strength == 2:
            signal_strength_text = "قوية" if lang == 'ar' else "Strong"
            signal_stars = "⭐⭐⭐⭐"
        elif signal_strength == 1:
            signal_strength_text = "متوسطة" if lang == 'ar' else "Moderate"
            signal_stars = "⭐⭐⭐"
        else:
            signal_strength_text = "ضعيفة" if lang == 'ar' else "Weak"
            signal_stars = "⭐⭐"

        # استخراج مستويات الدعم والمقاومة من التحليل
        support_level = price * 0.95  # قيمة افتراضية
        resistance_level = price * 1.05  # قيمة افتراضية

        # محاولة استخراج مستويات الدعم والمقاومة من استجابة Gemini إذا كانت متاحة
        if response and len(response) > 10:
            # البحث عن أنماط مستويات الدعم والمقاومة في النص
            support_patterns = [
                r'دعم.*?(\d+\.\d+)',
                r'مستوى الدعم.*?(\d+\.\d+)',
                r'support.*?(\d+\.\d+)',
                r'Support level.*?(\d+\.\d+)'
            ]

            resistance_patterns = [
                r'مقاومة.*?(\d+\.\d+)',
                r'مستوى المقاومة.*?(\d+\.\d+)',
                r'resistance.*?(\d+\.\d+)',
                r'Resistance level.*?(\d+\.\d+)'
            ]

            for pattern in support_patterns:
                matches = re.findall(pattern, response)
                if matches:
                    try:
                        support_level = float(matches[0])
                        break
                    except:
                        pass

            for pattern in resistance_patterns:
                matches = re.findall(pattern, response)
                if matches:
                    try:
                        resistance_level = float(matches[0])
                        break
                    except:
                        pass

        # إنشاء تحليل جديد بتنسيق مختلف تماماً
        if lang == 'ar':
            # تحضير قيمة RSI بشكل صحيح
            rsi_value = valid_indicators.get('RSI')
            if rsi_value not in [None, 'nan', 'N/A']:
                try:
                    rsi_display = f"{float(rsi_value):.2f}"
                except:
                    rsi_display = "غير متوفر"
            else:
                rsi_display = "غير متوفر"

            # تنسيق عربي جديد مع محاذاة النص إلى اليمين - تم تحديثه لإصلاح مشكلة النقطتين
            try:
                price_formatted = f"{price:.4f}" if isinstance(price, (int, float)) else str(price)
                price_change_formatted = f"{price_change:+.2f}" if isinstance(price_change, (int, float)) else str(price_change)
            except Exception as e:
                logger.error(f"خطأ في تنسيق السعر: {str(e)}")
                price_formatted = str(price)
                price_change_formatted = str(price_change)

            # الحصول على العملة المستهدفة من البيانات
            target_currency = market_data.get('currency', 'USD')
            currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

            new_analysis = f"""📊 تحليل عملة {symbol} (بواسطة الذكاء الاصطناعي)

💰 **السعر الحالي** {price_formatted} {currency_symbol}
📊 **نسبة التغير** {price_change_formatted}%

📈 **1. تقييم الاتجاه العام**

يبدو أن الاتجاه العام لـ {symbol} {technical_analysis.get('EMA', 'غير محدد')}. هذا مدعوم بتقاطع المتوسطات المتحركة النسبية (EMA20 و EMA50)، حيث أن EMA20 ({valid_indicators.get('EMA20', 'غير متوفر')}) {('أعلى من' if valid_indicators.get('EMA20', 0) > valid_indicators.get('EMA50', 0) else 'أقل من')} EMA50 ({valid_indicators.get('EMA50', 'غير متوفر')}). هذا يشير إلى زخم {('صعودي' if valid_indicators.get('EMA20', 0) > valid_indicators.get('EMA50', 0) else 'هبوطي')} على المدى القصير.

🔍 **2. مستويات الدعم والمقاومة المحتملة**

• **مستوى الدعم** {support_level:.4f} (تقريباً نفس قيمة المتوسطات المتحركة)
• **مستوى المقاومة** {resistance_level:.4f} (يقع ضمن نطاق بولينجر العلوي)
• هدف آخر محتمل يقع عند {price * 1.03:.4f} (وقد يواجه السعر صعوبة في تجاوزه)

🔮 **3. نظرة مستقبلية قصيرة المدى**

في المدى القصير، من المتوقع أن يستمر السعر في التحرك ضمن نطاق التداول الحالي، مع إمكانية اختراق مستوى المقاومة {resistance_level:.4f} إذا استمر الزخم الصعودي. مع ذلك، يجب الحذر من أن مؤشر القوة النسبية (RSI) ({rsi_display}) {('يشير إلى حالة تشبع شراء' if rsi_value and float(rsi_value) > 70 else 'يشير إلى حالة تشبع بيع' if rsi_value and float(rsi_value) < 30 else 'في نطاق متوازن')}. مؤشر MACD {technical_analysis.get('MACD', 'يعطي إشارة محايدة')} التي قد تؤدي إلى تصحيح سعري.

⭐️ **التوصية**

{recommendation_emoji} **{recommendation_text}**. بناءً على إشارات المتوسطات المتحركة النسبية ومؤشر MACD، {('مع الأخذ في الاعتبار حالة التشبع ' + symbol + ' (بولينجر)' if 'تشبع' in technical_analysis.get('Bollinger Bands', '') else '')}. فإن التوصية هي {recommendation_text} حيث أن السعر لا يزال قريباً من نطاق الدعم {('والمؤشرات تدعم الشراء' if recommendation_text == 'شراء' else 'والمؤشرات تدعم البيع' if recommendation_text == 'بيع' else 'والمؤشرات متضاربة')}.

⏰ {datetime.now().strftime('%H:%M')}"""
        else:
            # تحضير قيمة RSI بشكل صحيح
            rsi_value = valid_indicators.get('RSI')
            if rsi_value not in [None, 'nan', 'N/A']:
                try:
                    rsi_display = f"{float(rsi_value):.2f}"
                except:
                    rsi_display = "N/A"
            else:
                rsi_display = "N/A"

            # تنسيق إنجليزي جديد مع تحسين المظهر - تم تحديثه لإصلاح مشكلة النقطتين
            try:
                price_formatted = f"{price:.4f}" if isinstance(price, (int, float)) else str(price)
                price_change_formatted = f"{price_change:+.2f}" if isinstance(price_change, (int, float)) else str(price_change)
            except Exception as e:
                logger.error(f"خطأ في تنسيق السعر: {str(e)}")
                price_formatted = str(price)
                price_change_formatted = str(price_change)

            # الحصول على العملة المستهدفة من البيانات
            target_currency = market_data.get('currency', 'USD')
            currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

            new_analysis = f"""📊 Analysis for {symbol} (AI-powered)

💰 **Current Price** {price_formatted} {currency_symbol}
📊 **Change** {price_change_formatted}%

📈 **1. Overall Trend Assessment**

The general trend for {symbol} appears to be {technical_analysis.get('EMA', 'undefined')}. This is supported by the crossover of Exponential Moving Averages (EMA20 and EMA50), where EMA20 ({valid_indicators.get('EMA20', 'N/A')}) is {('above' if valid_indicators.get('EMA20', 0) > valid_indicators.get('EMA50', 0) else 'below')} EMA50 ({valid_indicators.get('EMA50', 'N/A')}). This indicates {('bullish' if valid_indicators.get('EMA20', 0) > valid_indicators.get('EMA50', 0) else 'bearish')} momentum in the short term.

🔍 **2. Potential Support and Resistance Levels**

• **Support Level** {support_level:.4f} (approximately the same value as the moving averages)
• **Resistance Level** {resistance_level:.4f} (falls within the upper Bollinger Band range)
• Another potential target is at {price * 1.03:.4f} (price may face difficulty breaking through)

🔮 **3. Short-term Outlook**

In the short term, the price is expected to continue moving within the current trading range, with the possibility of breaking through the resistance level at {resistance_level:.4f} if the bullish momentum continues. However, caution is advised as the Relative Strength Index (RSI) ({rsi_display}) {('indicates overbought conditions' if rsi_value and float(rsi_value) > 70 else 'indicates oversold conditions' if rsi_value and float(rsi_value) < 30 else 'is in a balanced range')}. The MACD indicator {technical_analysis.get('MACD', 'gives a neutral signal')} which may lead to a price correction.

⭐️ **Recommendation**

{recommendation_emoji} **{recommendation_text}**. Based on the signals from the Exponential Moving Averages and the MACD indicator, {('taking into account the ' + symbol + ' overbought/oversold condition (Bollinger)' if 'overbought' in technical_analysis.get('Bollinger Bands', '') or 'oversold' in technical_analysis.get('Bollinger Bands', '') else '')}. The recommendation is to {recommendation_text} as the price is still close to the support range {('and indicators support buying' if recommendation_text == 'BUY' else 'and indicators support selling' if recommendation_text == 'SELL' else 'and indicators are conflicting')}.

⏰ {datetime.now().strftime('%H:%M')}"""

        # استخدام دالة format_gemini_analysis المحسنة لتنسيق التحليل
        formatted_analysis = format_gemini_analysis(response, symbol, price, price_change, lang)

        # تطبيق التنظيف البسيط للنصوص بدلاً من fix_bold_formatting المعقدة
        try:
            from utils.utils import simple_ai_text_cleanup
            formatted_analysis = simple_ai_text_cleanup(formatted_analysis, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة simple_ai_text_cleanup")

        # إضافة الروابط المباشرة
        from .traditional_analysis import generate_direct_links
        direct_links = generate_direct_links(symbol)

        if lang == 'ar':
            links_text = f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
        else:
            links_text = f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

        formatted_analysis += links_text

        # إضافة سجل للتشخيص
        logger.info(f"تم إنشاء تحليل محسن جديد باللغة {lang} بطول {len(formatted_analysis)} حرف")
        logger.info(f"التحليل النهائي المحسن: {formatted_analysis[:100]}...")

        return formatted_analysis



    except Exception as e:
        logger.error(f"خطأ في تحليل Gemini: {str(e)}")
        return None

def create_analysis_prompt(symbol: str, price_data: list, indicators: Dict[str, Any], market_info: Dict[str, Any], lang: str) -> str:
    """
    إنشاء سياق للتحليل

    Args:
        symbol: رمز العملة
        price_data: بيانات الأسعار
        indicators: المؤشرات الفنية
        market_info: معلومات السوق
        lang: لغة التحليل

    Returns:
        سياق التحليل
    """
    # تحويل البيانات إلى تنسيق نصي
    price_data_str = json.dumps(price_data, indent=2)
    indicators_str = json.dumps(indicators, indent=2)
    market_info_str = json.dumps(market_info, indent=2)

    if lang == 'ar':
        prompt = f"""
        أنت محلل فني محترف للعملات الرقمية. قم بتحليل البيانات التالية وتقديم تحليل فني شامل باللغة العربية.

        العملة: {symbol}

        بيانات الأسعار الأخيرة:
        {price_data_str}

        المؤشرات الفنية:
        {indicators_str}

        معلومات السوق:
        {market_info_str}

        قدم تحليلاً فنياً يتضمن:
        1. الاتجاه العام للسعر
        2. مستويات الدعم والمقاومة الرئيسية (أعط أرقاماً محددة)
        3. تحديد السيناريوهات السعرية المحتملة (صعودي، هبوطي، جانبي) مع تحديد المستويات السعرية لكل سيناريو
        4. اقتراح استراتيجيات تداول محددة مع نقاط دخول وخروج وأوامر وقف الخسارة
        5. توصية واضحة وصريحة (شراء، بيع، أو انتظار) مع تبرير التوصية

        قواعد مهمة:
        - قدم التحليل بتنسيق سهل القراءة مع استخدام الرموز التعبيرية المناسبة
        - اجعل التوصية واضحة جداً وضعها في فقرة منفصلة مع عنوان "⭐️ **التوصية**" (بدون نقطتين)
        - حدد مستويات الدعم والمقاومة بأرقام دقيقة (مثال: **مستوى الدعم** 0.2500، **مستوى المقاومة** 0.2700)
        - لا تضع النقطتين داخل النص العريض، بل ضعها خارجه أو لا تستخدمها
        - لا تضف أي تنبيهات أو ملاحظات حول كون التحليل لأغراض تعليمية فقط
        - لا تضف أي إخلاء مسؤولية في نهاية التحليل
        - ركز على تقديم تحليل مفصل ومفيد مع الحفاظ على التفاصيل المهمة
        - قدم سيناريوهات سعرية محددة بالأرقام وليس بشكل عام
        - اقترح استراتيجية تداول محددة مع نقاط دخول وخروج وأوامر وقف الخسارة بالأرقام
        """
    else:
        prompt = f"""
        You are a professional technical analyst for cryptocurrencies. Analyze the following data and provide a comprehensive technical analysis in English.

        Currency: {symbol}

        Recent price data:
        {price_data_str}

        Technical indicators:
        {indicators_str}

        Market information:
        {market_info_str}

        Provide a technical analysis that includes:
        1. General price trend
        2. Key support and resistance levels (give specific numbers)
        3. Identify potential price scenarios (bullish, bearish, sideways) with specific price levels for each scenario
        4. Suggest specific trading strategies with entry/exit points and stop-loss orders
        5. Clear and explicit recommendation (buy, sell, or hold) with justification

        Important rules:
        - Provide the analysis in an easy-to-read format with appropriate emojis
        - Make the recommendation very clear and put it in a separate paragraph with the heading "⭐️ **Recommendation**" (without colon)
        - Specify support and resistance levels with precise numbers (example: **Support level** 0.2500, **Resistance level** 0.2700)
        - Do not put colons inside bold text, either place them outside or don't use them
        - Do not add any disclaimers or notes about the analysis being for educational purposes only
        - Do not add any disclaimers at the end of the analysis
        - Focus on providing detailed and useful analysis while maintaining important details
        - Provide specific price scenarios with numbers, not just general statements
        - Suggest a specific trading strategy with entry/exit points and stop-loss orders with numbers
        """

    return prompt

def clean_and_format_analysis(analysis: str, lang: str) -> str:
    """
    تنظيف وتنسيق نتيجة التحليل

    Args:
        analysis: نص التحليل
        lang: لغة التحليل

    Returns:
        نص التحليل المنسق
    """
    # إزالة الأسطر الفارغة المتكررة
    while '\n\n\n' in analysis:
        analysis = analysis.replace('\n\n\n', '\n\n')

    # إضافة عنوان
    if lang == 'ar':
        header = "# ✨ تحليل متقدم بالذكاء الاصطناعي ✨\n\n"
        footer = ""  # إزالة التنبيه
    else:
        header = "# ✨ Advanced AI Analysis ✨\n\n"
        footer = ""  # إزالة التنبيه

    # دمج النص
    formatted_analysis = header + analysis + footer

    return formatted_analysis

async def generate_smart_alerts(model, symbol: str, current_price: float, indicators: Dict[str, Any], lang: str = 'ar') -> Dict[str, float]:
    """
    إنشاء تنبيهات ذكية باستخدام Gemini

    Args:
        model: نموذج Gemini
        symbol: رمز العملة
        current_price: السعر الحالي
        indicators: المؤشرات الفنية
        lang: لغة التحليل

    Returns:
        قاموس يحتوي على مستويات التنبيه
    """
    try:
        # تحويل المؤشرات إلى تنسيق نصي
        indicators_str = json.dumps(indicators, indent=2)

        # إنشاء سياق للنموذج
        if lang == 'ar':
            prompt = f"""
            أنت خبير في التحليل الفني للعملات الرقمية. بناءً على البيانات التالية لعملة {symbol}، اقترح 3 مستويات سعرية مهمة للتنبيه:

            السعر الحالي: {current_price}
            المؤشرات الفنية: {indicators_str}

            اقترح:
            1. مستوى سعر مهم أعلى من السعر الحالي (مقاومة) - يجب أن يكون رقماً محدداً
            2. مستوى سعر مهم أقل من السعر الحالي (دعم) - يجب أن يكون رقماً محدداً
            3. مستوى سعر حرج يشير إلى تغير الاتجاه - يجب أن يكون رقماً محدداً

            قدم الأرقام فقط بدون أي نص إضافي، مفصولة بفواصل، بهذا الترتيب: مقاومة,دعم,تغير_اتجاه

            مثال على الإجابة المطلوبة: 0.2850,0.2750,0.2650
            """
        else:
            prompt = f"""
            You are an expert in technical analysis for cryptocurrencies. Based on the following data for {symbol}, suggest 3 important price levels for alerts:

            Current price: {current_price}
            Technical indicators: {indicators_str}

            Suggest:
            1. An important price level above the current price (resistance) - must be a specific number
            2. An important price level below the current price (support) - must be a specific number
            3. A critical price level indicating a trend change - must be a specific number

            Provide only the numbers without any additional text, separated by commas, in this order: resistance,support,trend_change

            Example of expected answer: 0.2850,0.2750,0.2650
            """

        # استدعاء نموذج Gemini
        response = await model.generate_content_async(prompt)

        if not response or not response.text:
            logger.warning("لم يتم الحصول على استجابة من Gemini API")
            return {}

        # تحليل الاستجابة
        try:
            levels_text = response.text.strip()
            resistance, support, trend_change = levels_text.split(',')

            return {
                'resistance': float(resistance.strip()),
                'support': float(support.strip()),
                'trend_change': float(trend_change.strip())
            }
        except Exception as e:
            logger.error(f"خطأ في تحليل مستويات التنبيه: {str(e)}")
            return {}

    except Exception as e:
        logger.error(f"خطأ في إنشاء تنبيهات ذكية: {str(e)}")
        return {}




async def get_user_api_client(user_id: str, api_type: str):
    """
    الحصول على عميل API للمستخدم

    Args:
        user_id: معرف المستخدم
        api_type: نوع API ('gemini' أو 'binance')

    Returns:
        عميل API مهيأ أو None إذا لم تكن هناك مفاتيح متاحة
    """
    try:
        # استخدام مدير API من المتغير العام
        import sys
        # استخدام مدير API من bot.py
        from firebase_admin import firestore
        from cryptography.fernet import Fernet
        db = firestore.client()

        # الحصول على مفتاح التشفير
        key_ref = db.collection('system_config').document('encryption_key')
        key_data = key_ref.get()

        if key_data.exists and 'key' in key_data.to_dict():
            encryption_key = key_data.to_dict()['key']
            cipher = Fernet(encryption_key.encode())

            # استخدام مفتاح API المخزن للمستخدم
            api_keys_ref = db.collection('user_api_keys').document(user_id)
            api_keys_doc = api_keys_ref.get()

            if api_keys_doc.exists:
                api_keys_data = api_keys_doc.to_dict()

                if api_type == 'gemini':
                    # الحصول على مفتاح API لـ Gemini
                    encrypted_key = api_keys_data.get('gemini_key')

                    if encrypted_key:
                        try:
                            # فك تشفير المفتاح
                            api_key = cipher.decrypt(encrypted_key.encode()).decode()

                            # تهيئة عميل Gemini
                            import google.generativeai as genai
                            logger.info(f"تهيئة Gemini API للمستخدم {user_id} باستخدام مفتاح المستخدم")
                            genai.configure(api_key=api_key)

                            # استخدام نموذج gemini-2.0-flash-exp (النموذج الأساسي الجديد)
                            logger.info("محاولة إنشاء نموذج gemini-2.0-flash-exp")
                            model = genai.GenerativeModel('gemini-2.0-flash-exp')
                            logger.info("تم إنشاء نموذج gemini-2.0-flash-exp بنجاح")

                            # اختبار النموذج للتأكد من أنه يعمل
                            try:
                                test_response = await asyncio.to_thread(
                                    lambda: model.generate_content("Test").text
                                )
                                if test_response:
                                    logger.info(f"تم إنشاء واختبار نموذج Gemini 2.0 Flash بنجاح للمستخدم {user_id}")
                                    return model
                            except Exception as test_error:
                                logger.warning(f"فشل اختبار نموذج Gemini للمستخدم {user_id}: {str(test_error)}")
                                # نستمر للمحاولة البديلة

                            # محاولة بديلة باستخدام المفتاح الافتراضي
                            logger.info("محاولة استخدام المفتاح الافتراضي لـ Gemini")
                            import config as config_module
                            if hasattr(config_module, 'DEFAULT_GEMINI_API_KEY') and config_module.DEFAULT_GEMINI_API_KEY:
                                genai.configure(api_key=config_module.DEFAULT_GEMINI_API_KEY)
                                model = genai.GenerativeModel('gemini-2.0-flash')
                                logger.info("تم إنشاء نموذج Gemini 2.0 Flash باستخدام المفتاح الافتراضي")
                                return model
                            else:
                                logger.error("المفتاح الافتراضي لـ Gemini غير متوفر في ملف التكوين")
                                return None

                        except Exception as e:
                            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
                            # محاولة بديلة باستخدام المفتاح الافتراضي
                            try:
                                import config as config_module
                                if hasattr(config_module, 'DEFAULT_GEMINI_API_KEY') and config_module.DEFAULT_GEMINI_API_KEY:
                                    logger.info("محاولة استخدام المفتاح الافتراضي لـ Gemini بعد الخطأ")
                                    genai.configure(api_key=config_module.DEFAULT_GEMINI_API_KEY)
                                    return genai.GenerativeModel('gemini-2.0-flash')
                                else:
                                    logger.error("المفتاح الافتراضي لـ Gemini غير متوفر في ملف التكوين")
                                    return None
                            except Exception as e2:
                                logger.error(f"فشلت المحاولة البديلة لاستدعاء Gemini API: {str(e2)}")
                                return None

                elif api_type == 'binance':
                    # الحصول على مفاتيح API لـ Binance
                    encrypted_key = api_keys_data.get('binance_key')
                    encrypted_secret = api_keys_data.get('binance_secret')

                    if encrypted_key and encrypted_secret:
                        try:
                            # فك تشفير المفاتيح
                            api_key = cipher.decrypt(encrypted_key.encode()).decode()
                            api_secret = cipher.decrypt(encrypted_secret.encode()).decode()

                            # تهيئة عميل Binance
                            from binance.client import Client
                            return Client(api_key, api_secret)
                        except Exception as e:
                            logger.error(f"خطأ في استدعاء Binance API: {str(e)}")
                            return None

        # إذا لم يتم العثور على مفتاح للمستخدم، استخدم المفتاح الافتراضي
        if api_type == 'gemini':
            try:
                import config as config_module
                import google.generativeai as genai

                if hasattr(config_module, 'DEFAULT_GEMINI_API_KEY') and config_module.DEFAULT_GEMINI_API_KEY:
                    logger.info(f"استخدام المفتاح الافتراضي لـ Gemini للمستخدم {user_id}")
                    genai.configure(api_key=config_module.DEFAULT_GEMINI_API_KEY)

                    # استخدام نموذج gemini-2.0-flash
                    logger.info("محاولة إنشاء نموذج gemini-2.0-flash باستخدام المفتاح الافتراضي")
                    model = genai.GenerativeModel('gemini-2.0-flash')
                    logger.info("تم إنشاء نموذج gemini-2.0-flash بنجاح باستخدام المفتاح الافتراضي")

                    # اختبار النموذج للتأكد من أنه يعمل
                    try:
                        test_response = await asyncio.to_thread(
                            lambda: model.generate_content("Test").text
                        )
                        if test_response:
                            logger.info(f"تم إنشاء واختبار نموذج Gemini 2.0 Flash بنجاح باستخدام المفتاح الافتراضي")
                            return model
                    except Exception as test_error:
                        logger.warning(f"فشل اختبار نموذج Gemini باستخدام المفتاح الافتراضي: {str(test_error)}")
                        return None
                else:
                    logger.error("المفتاح الافتراضي لـ Gemini غير متوفر في ملف التكوين")
                    return None

            except Exception as e:
                logger.error(f"خطأ في استخدام مفتاح Gemini الافتراضي: {str(e)}")
                return None

        return None

    except Exception as e:
        logger.error(f"خطأ في الحصول على عميل API لـ {api_type}: {str(e)}")

        # محاولة أخيرة باستخدام المفتاح الافتراضي
        if api_type == 'gemini':
            try:
                import config as config_module
                import google.generativeai as genai

                if hasattr(config_module, 'DEFAULT_GEMINI_API_KEY') and config_module.DEFAULT_GEMINI_API_KEY:
                    logger.info("المحاولة الأخيرة لاستخدام المفتاح الافتراضي لـ Gemini")
                    genai.configure(api_key=config_module.DEFAULT_GEMINI_API_KEY)

                    # استخدام نموذج gemini-2.0-flash-exp (النموذج الأساسي الجديد)
                    model = genai.GenerativeModel('gemini-2.0-flash-exp')
                    logger.info("تم إنشاء نموذج gemini-2.0-flash-exp بنجاح في المحاولة الأخيرة")
                    return model
                else:
                    logger.error("المفتاح الافتراضي لـ Gemini غير متوفر في ملف التكوين")
                    return None
            except Exception as e2:
                logger.error(f"فشلت المحاولة الأخيرة لاستخدام مفتاح Gemini الافتراضي: {str(e2)}")

        return None


async def get_trading_strategy(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]:
    """
    تحليل البيانات واقتراح استراتيجية تداول آلية

    Args:
        model: نموذج Gemini
        market_data: بيانات السوق والمؤشرات الفنية
        lang: لغة التحليل (ar أو en)

    Returns:
        نص استراتيجية التداول أو None إذا فشل التحليل
    """
    # إذا كان النموذج غير متاح، استخدم النموذج الافتراضي
    if model is None:
        logger.warning("نموذج Gemini غير متاح، محاولة استخدام النموذج الافتراضي")
        try:
            import google.generativeai as genai
            import config
            if hasattr(config, 'DEFAULT_GEMINI_API_KEY') and config.DEFAULT_GEMINI_API_KEY:
                genai.configure(api_key=config.DEFAULT_GEMINI_API_KEY)
                try:
                    model = genai.GenerativeModel('gemini-2.0-flash-exp')
                    logger.info("تم استخدام النموذج الافتراضي gemini-2.0-flash-exp")
                except Exception as e:
                    logger.error(f"فشل في استخدام النموذج gemini-2.0-flash-exp: {str(e)}")
                    return None
            else:
                logger.error("لا يوجد مفتاح Gemini API افتراضي")
                return None
        except Exception as e:
            logger.error(f"فشل في تهيئة النموذج الافتراضي: {str(e)}")
            return None
    try:
        if not market_data:
            logger.warning("لا توجد بيانات كافية لاقتراح استراتيجية تداول")
            return None

        # استخراج البيانات الأساسية
        symbol = market_data.get('symbol', 'Unknown')
        price = market_data.get('price', 0)
        price_change = market_data.get('price_change', 0)

        # الحصول على العملة المستهدفة
        target_currency = market_data.get('currency', 'USD')
        currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

        # استخراج المؤشرات الفنية
        indicators = {
            'RSI': market_data.get('rsi'),
            'EMA20': market_data.get('ema20'),
            'EMA50': market_data.get('ema50'),
            'MACD': market_data.get('macd'),
            'MACD Signal': market_data.get('macd_signal'),
            'MACD Histogram': market_data.get('macd_histogram'),
            'Bollinger Upper': market_data.get('bb_upper'),
            'Bollinger Middle': market_data.get('bb_middle'),
            'Bollinger Lower': market_data.get('bb_lower'),
            'Stochastic K': market_data.get('stoch_k'),
            'Stochastic D': market_data.get('stoch_d'),
            'ADX': market_data.get('adx'),
            'Plus DI': market_data.get('plus_di'),
            'Minus DI': market_data.get('minus_di')
        }

        # تصفية المؤشرات ذات القيم الصالحة
        valid_indicators = {k: v for k, v in indicators.items() if v not in [None, 'nan', 'N/A']}

        # إنشاء سياق للنموذج حسب اللغة
        if lang == 'ar':
            prompt = f"""
            أنت خبير في استراتيجيات التداول للعملات الرقمية. قم بتحليل البيانات التالية لعملة {symbol} واقترح استراتيجية تداول محددة ومفصلة:

            السعر الحالي: {price} {currency_symbol}
            نسبة التغير: {price_change}%

            المؤشرات الفنية:
            {json.dumps(valid_indicators, indent=2, ensure_ascii=False)}

            قم بتحديد:
            1. نوع الاستراتيجية المناسبة (كسر المقاومة، الارتداد من الدعم، تقاطع المتوسطات المتحركة، استراتيجية RSI، استراتيجية Bollinger Bands)
            2. نقطة الدخول المقترحة (سعر محدد بالأرقام)
            3. وقف الخسارة (سعر محدد بالأرقام ونسبة مئوية من سعر الدخول)
            4. هدف الربح (سعر محدد بالأرقام ونسبة مئوية من سعر الدخول)
            5. الإطار الزمني المقترح للتداول (ساعة، 4 ساعات، يوم)
            6. نسبة المخاطرة/المكافأة (أرقام محددة)
            7. الأساس المنطقي للاستراتيجية

            قواعد مهمة:
            - قدم الاستراتيجية بتنسيق سهل القراءة مع استخدام الرموز التعبيرية (الإيموجي) المناسبة لكل قسم
            - استخدم إيموجي مختلف لكل نقطة رئيسية. مثلاً: 📈 للاتجاه، 🎯 لنقطة الدخول، 🛑 لوقف الخسارة، 💰 لهدف الربح، ⏱️ للإطار الزمني، ⚖️ لنسبة المخاطرة/المكافأة، 🧠 للأساس المنطقي
            - لا تضف أي إخلاء مسؤولية أو تنبيهات
            - اجعل العناوين واضحة جداً وضعها في فقرة منفصلة مع عنوان "**نوع الاستراتيجية**" (بدون نقطتين)
            - لا تضع النقطتين داخل النص العريض، بل ضعها خارجه أو لا تستخدمها
            - قدم أرقاماً محددة لنقاط الدخول والخروج ووقف الخسارة، وليس نطاقات عامة
            - ركز على تقديم استراتيجية مفصلة ومفيدة مع الحفاظ على التفاصيل المهمة
            - استخدم النص العريض للمعلومات المهمة ومستويات الأسعار الرئيسية
            - قم بهيكلة تحليلك بأقسام واضحة ونقاط محددة
            - قم بتضمين نقاط دخول بديلة في حالة تحرك السعر قبل أن يتمكن المتداول من الدخول
            - قدم خطة تنفيذ خطوة بخطوة للاستراتيجية
            - قم بتضمين مؤشرات محددة يجب مراقبتها أثناء التداول
            """
        else:
            prompt = f"""
            You are an expert in trading strategies for cryptocurrencies. Analyze the following data for {symbol} and suggest a specific and detailed trading strategy:

            Current Price: {price} {currency_symbol}
            Price Change: {price_change}%

            Technical Indicators:
            {json.dumps(valid_indicators, indent=2)}

            Specify:
            1. Type of suitable strategy (Resistance Breakout, Support Bounce, Moving Average Crossover, RSI Strategy, Bollinger Bands Strategy)
            2. Suggested entry point (specific price with numbers)
            3. Stop loss (specific price with numbers and percentage from entry price)
            4. Take profit (specific price with numbers and percentage from entry price)
            5. Suggested timeframe for trading (1h, 4h, 1d)
            6. Risk/reward ratio (specific numbers)
            7. Rationale for the strategy

            Important rules:
            - Provide the strategy in an easy-to-read format with appropriate emojis for each section
            - Use a different emoji for each main point. For example: 📈 for trend, 🎯 for entry point, 🛑 for stop loss, 💰 for take profit, ⏱️ for timeframe, ⚖️ for risk/reward ratio, 🧠 for rationale
            - Do not add any disclaimers or warnings
            - Make the headings very clear and put them in a separate paragraph with the heading "**Strategy Type**" (without colon)
            - Do not put colons inside bold text, either place them outside or don't use them
            - Provide specific numbers for entry, exit, and stop-loss points, not general ranges
            - Focus on providing detailed and useful strategy while maintaining important details
            - Use bold text for important information and key price levels
            - Structure your analysis with clear sections and bullet points
            - Include alternative entry points if the price moves before the trader can enter
            - Provide a step-by-step implementation plan for the strategy
            - Include specific indicators to monitor during the trade
            """

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini لاقتراح استراتيجية تداول باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            response_obj = await asyncio.to_thread(
                lambda: model.generate_content(prompt)
            )
            response = response_obj.text
            logger.info(f"تم الحصول على استجابة من Gemini بطول {len(response)} حرف")
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return None

        # تطبيق التنسيق المبهر المحسن
        try:
            from utils.utils import fix_bold_formatting
            response = fix_bold_formatting(response, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting")

        # تنسيق الاستجابة وإصلاح مشكلة النقطتين في النص العربي
        if lang == 'ar':
            header = f"📊 استراتيجية مقترحة لـ {symbol}\n\n"

            # إصلاح تنسيق النص العربي (إزالة النقطتين من داخل النص العريض)
            response = response.replace("**نوع الاستراتيجية:**", "**نوع الاستراتيجية**")
            response = response.replace("**نقطة الدخول:**", "**نقطة الدخول**")
            response = response.replace("**وقف الخسارة:**", "**وقف الخسارة**")
            response = response.replace("**هدف الربح:**", "**هدف الربح**")
            response = response.replace("**الإطار الزمني:**", "**الإطار الزمني**")
            response = response.replace("**نسبة المخاطرة/المكافأة:**", "**نسبة المخاطرة/المكافأة**")
            response = response.replace("**الأساس المنطقي:**", "**الأساس المنطقي**")

            # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
            response = re.sub(r'\*\*(.*?):\*\*', r'**\1**', response)
        else:
            header = f"📊 Suggested Strategy for {symbol}:\n\n"

        # إضافة الروابط المباشرة
        from .traditional_analysis import generate_direct_links
        direct_links = generate_direct_links(symbol)

        if lang == 'ar':
            links_text = f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
        else:
            links_text = f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

        return header + response + links_text

    except Exception as e:
        logger.error(f"خطأ في اقتراح استراتيجية تداول: {str(e)}")
        return None


async def get_price_prediction(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]:
    """
    تقديم تنبؤات سعرية للفترات القادمة

    Args:
        model: نموذج Gemini
        market_data: بيانات السوق والمؤشرات الفنية
        lang: لغة التحليل (ar أو en)

    Returns:
        نص التنبؤات السعرية أو None إذا فشل التحليل
    """
    try:
        if not market_data:
            logger.warning("لا توجد بيانات كافية للتنبؤ بالأسعار")
            return None

        # استخراج البيانات الأساسية
        symbol = market_data.get('symbol', 'Unknown')
        price = market_data.get('price', 0)
        price_change = market_data.get('price_change', 0)

        # الحصول على العملة المستهدفة
        target_currency = market_data.get('currency', 'USD')
        currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

        # استخراج المؤشرات الفنية
        indicators = {
            'RSI': market_data.get('rsi'),
            'EMA20': market_data.get('ema20'),
            'EMA50': market_data.get('ema50'),
            'MACD': market_data.get('macd'),
            'MACD Signal': market_data.get('macd_signal'),
            'Bollinger Upper': market_data.get('bb_upper'),
            'Bollinger Middle': market_data.get('bb_middle'),
            'Bollinger Lower': market_data.get('bb_lower')
        }

        # تصفية المؤشرات ذات القيم الصالحة
        valid_indicators = {k: v for k, v in indicators.items() if v not in [None, 'nan', 'N/A']}

        # إنشاء سياق للنموذج حسب اللغة
        if lang == 'ar':
            prompt = f"""
            أنت خبير في تحليل وتوقع أسعار العملات الرقمية. قم بتحليل البيانات التالية لعملة {symbol} وقدم توقعات سعرية للفترات القادمة:

            السعر الحالي: {price} {currency_symbol}
            نسبة التغير: {price_change}%

            المؤشرات الفنية:
            {json.dumps(valid_indicators, indent=2, ensure_ascii=False)}

            قدم توقعات سعرية لـ:
            1. الـ 24 ساعة القادمة (سيناريو صعودي، محايد، هبوطي مع نسبة احتمالية لكل سيناريو)
            2. الـ 7 أيام القادمة (سيناريو صعودي، محايد، هبوطي مع نسبة احتمالية لكل سيناريو)

            لكل سيناريو، حدد نطاق سعري متوقع (من سعر إلى سعر) بأرقام محددة.

            قواعد مهمة:
            - قدم التوقعات بتنسيق سهل القراءة مع استخدام الرموز التعبيرية (الإيموجي) المناسبة لكل قسم
            - استخدم إيموجي مختلف لكل سيناريو وفترة زمنية. مثلاً: 📅 للفترة الزمنية، 📈 للسيناريو الصعودي، ↔️ للسيناريو المحايد، 📉 للسيناريو الهبوطي، 💹 للنطاق السعري، 📊 للاحتمالية
            - لا تضف أي إخلاء مسؤولية أو تنبيهات
            - اجعل العناوين واضحة جداً وضعها في فقرة منفصلة مع عنوان "**توقعات 24 ساعة**" (بدون نقطتين)
            - لا تضع النقطتين داخل النص العريض، بل ضعها خارجه أو لا تستخدمها
            - قدم أرقاماً محددة للنطاقات السعرية المتوقعة، وليس نطاقات عامة
            - ركز على تقديم تنبؤات مفصلة ومفيدة مع الحفاظ على التفاصيل المهمة
            - استخدم رموز الأعلام لكل عملة (مثلاً: 🇺🇸 للدولار الأمريكي، 🇸🇦 للريال السعودي)
            """
        else:
            prompt = f"""
            You are an expert in analyzing and forecasting cryptocurrency prices. Analyze the following data for {symbol} and provide price predictions for upcoming periods:

            Current Price: {price} {currency_symbol}
            Price Change: {price_change}%

            Technical Indicators:
            {json.dumps(valid_indicators, indent=2)}

            Provide price predictions for:
            1. Next 24 hours (bullish, neutral, bearish scenario with probability percentage for each)
            2. Next 7 days (bullish, neutral, bearish scenario with probability percentage for each)

            For each scenario, specify an expected price range (from price to price) with specific numbers.

            Important rules:
            - Provide the predictions in an easy-to-read format with appropriate emojis for each section
            - Use a different emoji for each scenario and timeframe. For example: 📅 for timeframe, 📈 for bullish scenario, ↔️ for neutral scenario, 📉 for bearish scenario, 💹 for price range, 📊 for probability
            - Do not add any disclaimers or warnings
            - Make the headings very clear and put them in a separate paragraph with the heading "**24-Hour Forecast**" (without colon)
            - Do not put colons inside bold text, either place them outside or don't use them
            - Provide specific numbers for price ranges, not just general statements
            - Focus on providing detailed and useful predictions while maintaining important details
            - Use bold text for important information and key price levels
            - Structure your analysis with clear sections and bullet points
            - Include specific support and resistance levels with exact numbers
            - For each scenario, provide exact probability percentages
            - Make sure to include entry and exit points with specific price numbers
            - Include flag emojis for each currency (e.g., 🇺🇸 for USD, 🇸🇦 for SAR)
            """

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini للتنبؤ بالأسعار باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            response_obj = await asyncio.to_thread(
                lambda: model.generate_content(prompt)
            )
            response = response_obj.text
            logger.info(f"تم الحصول على استجابة من Gemini بطول {len(response)} حرف")
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return None

        # تطبيق التنظيف البسيط للنصوص
        try:
            from utils.utils import simple_ai_text_cleanup
            response = simple_ai_text_cleanup(response, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة simple_ai_text_cleanup")

        # تنسيق الاستجابة وإصلاح مشكلة النقطتين في النص العربي
        if lang == 'ar':
            header = f"🔮 تنبؤات سعرية لـ {symbol}\n\n"

            # إزالة النجوم الزائدة وتحسين التنسيق أولاً
            # استبدال النجوم المتعددة بتنسيق bold بسيط
            response = re.sub(r'\*{3,}([^*]+)\*{3,}', r'**\1**', response)  # *** أو أكثر إلى **
            response = re.sub(r'\*{5,}', '**', response)  # إزالة النجوم الزائدة

            # إصلاح النص العريض المكسور
            response = re.sub(r'\*\*([^*\n]+)(?!\*\*)', r'**\1**', response)

            # إصلاح تنسيق النص العربي (إزالة النقطتين من داخل النص العريض)
            response = response.replace("**توقعات 24 ساعة:**", "**توقعات 24 ساعة**")
            response = response.replace("**توقعات 7 أيام:**", "**توقعات 7 أيام**")
            response = response.replace("**سيناريو صعودي:**", "**سيناريو صعودي**")
            response = response.replace("**سيناريو محايد:**", "**سيناريو محايد**")
            response = response.replace("**سيناريو هبوطي:**", "**سيناريو هبوطي**")

            # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
            response = re.sub(r'\*\*(.*?):\*\*', r'**\1**', response)
        else:
            header = f"🔮 Price Predictions for {symbol}:\n\n"

        # إضافة الروابط المباشرة
        from .traditional_analysis import generate_direct_links
        direct_links = generate_direct_links(symbol)

        if lang == 'ar':
            links_text = f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
        else:
            links_text = f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

        return header + response + links_text

    except Exception as e:
        logger.error(f"خطأ في التنبؤ بالأسعار: {str(e)}")
        return None


async def get_ichimoku_analysis(model, market_data: Dict[str, Any], ichimoku_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]:
    """
    تحليل مؤشر سحابة إيشيموكو (Ichimoku Cloud) للعملة

    Args:
        model: نموذج Gemini
        market_data: بيانات السوق والمؤشرات الفنية
        ichimoku_data: بيانات مؤشر إيشيموكو
        lang: لغة التحليل (ar أو en)

    Returns:
        نص تحليل مؤشر إيشيموكو أو None إذا فشل التحليل
    """
    try:
        if not market_data:
            logger.warning("لا توجد بيانات كافية لتحليل مؤشر إيشيموكو")
            return None

        # استخراج البيانات الأساسية
        symbol = market_data.get('symbol', 'UNKNOWN')
        price = market_data.get('price', 'N/A')
        price_change = market_data.get('price_change_24h', 'N/A')

        # استخراج بيانات مؤشر إيشيموكو من ichimoku_data أو market_data
        if ichimoku_data:
            ichimoku_tenkan = ichimoku_data.get('tenkan', market_data.get('ichimoku_tenkan', 'N/A'))
            ichimoku_kijun = ichimoku_data.get('kijun', market_data.get('ichimoku_kijun', 'N/A'))
            ichimoku_senkou_a = ichimoku_data.get('senkou_a', market_data.get('ichimoku_senkou_a', 'N/A'))
            ichimoku_senkou_b = ichimoku_data.get('senkou_b', market_data.get('ichimoku_senkou_b', 'N/A'))
            ichimoku_chikou = ichimoku_data.get('chikou', market_data.get('ichimoku_chikou', 'N/A'))
        else:
            ichimoku_tenkan = market_data.get('ichimoku_tenkan', 'N/A')
            ichimoku_kijun = market_data.get('ichimoku_kijun', 'N/A')
            ichimoku_senkou_a = market_data.get('ichimoku_senkou_a', 'N/A')
            ichimoku_senkou_b = market_data.get('ichimoku_senkou_b', 'N/A')
            ichimoku_chikou = market_data.get('ichimoku_chikou', 'N/A')

        # التحقق من وجود بيانات إيشيموكو
        if ichimoku_tenkan in ['N/A', 'nan', None] or ichimoku_kijun in ['N/A', 'nan', None]:
            logger.warning(f"بيانات مؤشر إيشيموكو غير متوفرة للعملة {symbol}")
            return None

        # إنشاء سياق للتحليل
        if lang == 'ar':
            prompt = f"""
            قم بتحليل مؤشر سحابة إيشيموكو (Ichimoku Cloud) للعملة {symbol} بناءً على البيانات التالية:

            معلومات العملة:
            - الرمز: {symbol}
            - السعر الحالي: {price}
            - التغير خلال 24 ساعة: {price_change}

            مكونات مؤشر إيشيموكو:
            - خط التحويل (Tenkan-sen): {ichimoku_tenkan}
            - خط الأساس (Kijun-sen): {ichimoku_kijun}
            - خط السبان A (Senkou Span A): {ichimoku_senkou_a}
            - خط السبان B (Senkou Span B): {ichimoku_senkou_b}
            - خط التأخير (Chikou Span): {ichimoku_chikou}

            المطلوب:
            1. شرح مفصل لحالة مؤشر إيشيموكو الحالية
            2. تحليل العلاقة بين خطوط المؤشر المختلفة
            3. تحديد ما إذا كان السعر فوق أو تحت السحابة
            4. تحديد اتجاه السوق (صعودي، هبوطي، أو محايد) بناءً على المؤشر
            5. تحديد مناطق الدعم والمقاومة المهمة
            6. تقديم توصية تداول واضحة (شراء، بيع، أو انتظار) مع تحديد نقاط الدخول والخروج ووقف الخسارة

            ملاحظات مهمة:
            - قدم تحليلاً دقيقاً ومفصلاً
            - استخدم لغة واضحة ومفهومة
            - قدم أرقاماً محددة للدعم والمقاومة ونقاط الدخول والخروج
            - لا تذكر أنك نموذج ذكاء اصطناعي أو أي إشارة إلى نفسك
            - لا تضيف أي إخلاء مسؤولية أو تحذيرات في نهاية التحليل
            """
        else:
            prompt = f"""
            Analyze the Ichimoku Cloud indicator for {symbol} based on the following data:

            Currency Information:
            - Symbol: {symbol}
            - Current Price: {price}
            - 24h Change: {price_change}

            Ichimoku Components:
            - Tenkan-sen (Conversion Line): {ichimoku_tenkan}
            - Kijun-sen (Base Line): {ichimoku_kijun}
            - Senkou Span A (Leading Span A): {ichimoku_senkou_a}
            - Senkou Span B (Leading Span B): {ichimoku_senkou_b}
            - Chikou Span (Lagging Span): {ichimoku_chikou}

            Requirements:
            1. Detailed explanation of the current Ichimoku Cloud state
            2. Analysis of the relationship between different Ichimoku lines
            3. Determine if the price is above or below the cloud
            4. Identify market trend (bullish, bearish, or neutral) based on the indicator
            5. Identify important support and resistance areas
            6. Provide a clear trading recommendation (buy, sell, or wait) with specific entry, exit, and stop-loss points

            Important Notes:
            - Provide accurate and detailed analysis
            - Use clear and understandable language
            - Provide specific numbers for support, resistance, entry, and exit points
            - Do not mention that you are an AI model or any reference to yourself
            - Do not add any disclaimers or warnings at the end of the analysis
            """

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini لتحليل مؤشر إيشيموكو باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            response_obj = await asyncio.to_thread(
                lambda: model.generate_content(prompt)
            )
            response = response_obj.text
            logger.info(f"تم الحصول على استجابة من Gemini بطول {len(response)} حرف")
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return None

        # تطبيق التنسيق المبهر المحسن
        try:
            from utils.utils import fix_bold_formatting
            response = fix_bold_formatting(response, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting")

        # تنسيق الاستجابة وإصلاح مشكلة النقطتين في النص العربي
        if lang == 'ar':
            header = f"☁️ تحليل مؤشر سحابة إيشيموكو لـ {symbol}\n\n"

            # إصلاح تنسيق النص العربي (إزالة النقطتين من داخل النص العريض)
            response = response.replace("**التحليل:**", "**التحليل**")
            response = response.replace("**الاتجاه:**", "**الاتجاه**")
            response = response.replace("**التوصية:**", "**التوصية**")
            response = response.replace("**الخلاصة:**", "**الخلاصة**")

            # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
            response = re.sub(r'\*\*(.*?):\*\*', r'**\1**', response)
        else:
            header = f"☁️ Ichimoku Cloud Analysis for {symbol}\n\n"

        return header + response

    except Exception as e:
        logger.error(f"خطأ في تحليل مؤشر إيشيموكو: {str(e)}")
        return None


async def get_multi_timeframe_analysis(model, market_data: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]], lang: str = 'ar') -> Optional[str]:
    """
    تحليل العملة عبر إطارات زمنية متعددة

    Args:
        model: نموذج Gemini
        market_data: بيانات السوق والمؤشرات الفنية للإطار الزمني الرئيسي
        timeframes_data: قاموس يحتوي على بيانات لكل إطار زمني
        lang: لغة التحليل (ar أو en)

    Returns:
        نص التحليل متعدد الإطارات الزمنية أو None إذا فشل التحليل
    """
    try:
        if not market_data or not timeframes_data:
            logger.warning("لا توجد بيانات كافية للتحليل متعدد الإطارات الزمنية")
            return None

        # استخراج البيانات الأساسية
        symbol = market_data.get('symbol', 'Unknown')
        price = market_data.get('price', 0)
        price_change = market_data.get('price_change', 0)

        # الحصول على العملة المستهدفة
        target_currency = market_data.get('currency', 'USD')
        currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

        # تحضير بيانات الإطارات الزمنية للنموذج
        timeframes_info = {}
        for timeframe, data in timeframes_data.items():
            # استخراج المؤشرات الفنية لكل إطار زمني
            indicators = {
                'RSI': data.get('rsi'),
                'EMA20': data.get('ema20'),
                'EMA50': data.get('ema50'),
                'MACD': data.get('macd'),
                'MACD Signal': data.get('macd_signal'),
                'Bollinger Upper': data.get('bb_upper'),
                'Bollinger Middle': data.get('bb_middle'),
                'Bollinger Lower': data.get('bb_lower')
            }
            # تصفية المؤشرات ذات القيم الصالحة
            valid_indicators = {k: v for k, v in indicators.items() if v not in [None, 'nan', 'N/A']}
            timeframes_info[timeframe] = valid_indicators

        # إنشاء سياق للنموذج حسب اللغة
        if lang == 'ar':
            prompt = f"""
            أنت خبير في التحليل الفني متعدد الإطارات الزمنية للعملات الرقمية. قم بتحليل البيانات التالية لعملة {symbol} عبر إطارات زمنية مختلفة:

            السعر الحالي: {price} {currency_symbol}
            نسبة التغير: {price_change}%

            بيانات الإطارات الزمنية:
            {json.dumps(timeframes_info, indent=2, ensure_ascii=False)}

            قدم تحليلاً لكل إطار زمني يتضمن:
            1. الاتجاه العام (صعودي، هبوطي، محايد)
            2. حالة المؤشرات الفنية الرئيسية
            3. أنماط السعر المهمة
            4. مستويات الدعم والمقاومة المحددة بالأرقام

            ثم قدم خلاصة تجمع بين تحليلات الإطارات الزمنية المختلفة وتوضح:
            1. التناقضات أو التأكيدات بين الإطارات الزمنية
            2. الإطار الزمني الأنسب للتداول حالياً
            3. توصية محددة بناءً على التحليل المتكامل مع نقاط دخول وخروج محددة بالأرقام

            قواعد مهمة:
            - قدم التحليل بتنسيق سهل القراءة مع استخدام الرموز التعبيرية (الإيموجي) المناسبة لكل قسم
            - استخدم إيموجي مختلف لكل إطار زمني ولكل نقطة رئيسية. مثلاً: ⏱️ للإطار الزمني، 📈 للاتجاه الصعودي، 📉 للاتجاه الهبوطي، ↔️ للاتجاه المحايد، 📊 للمؤشرات الفنية، 🔍 للأنماط، 📋 للخلاصة، ⚠️ للتناقضات، ✅ للتأكيدات، 🎯 للتوصية
            - لا تضف أي إخلاء مسؤولية أو تنبيهات
            - اجعل العناوين واضحة جداً وضعها في فقرة منفصلة مع عنوان "**إطار 1 ساعة**" (بدون نقطتين)
            - لا تضع النقطتين داخل النص العريض، بل ضعها خارجه أو لا تستخدمها
            - قدم أرقاماً محددة لمستويات الدعم والمقاومة ونقاط الدخول والخروج، وليس نطاقات عامة
            - ركز على تقديم تحليل مفصل ومفيد مع الحفاظ على التفاصيل المهمة
            - استخدم رموز الأعلام لكل عملة (مثلاً: 🇺🇸 للدولار الأمريكي، 🇸🇦 للريال السعودي)
            """
        else:
            prompt = f"""
            You are an expert in multi-timeframe technical analysis for cryptocurrencies. Analyze the following data for {symbol} across different timeframes:

            Current Price: {price} {currency_symbol}
            Price Change: {price_change}%

            Timeframes Data:
            {json.dumps(timeframes_info, indent=2)}

            Provide an analysis for each timeframe including:
            1. General trend (bullish, bearish, neutral)
            2. Status of key technical indicators
            3. Important price patterns
            4. Support and resistance levels with specific numbers

            Then provide a conclusion that combines the analyses from different timeframes and clarifies:
            1. Contradictions or confirmations between timeframes
            2. The most suitable timeframe for trading currently
            3. Specific recommendation based on the integrated analysis with entry and exit points with specific numbers

            Important rules:
            - Provide the analysis in an easy-to-read format with appropriate emojis for each section
            - Use a different emoji for each timeframe and each main point. For example: ⏱️ for timeframe, 📈 for bullish trend, 📉 for bearish trend, ↔️ for neutral trend, 📊 for technical indicators, 🔍 for patterns, 📋 for conclusion, ⚠️ for contradictions, ✅ for confirmations, 🎯 for recommendation
            - Do not add any disclaimers or warnings
            - Make the headings very clear and put them in a separate paragraph with the heading "**1-Hour Timeframe**" (without colon)
            - Do not put colons inside bold text, either place them outside or don't use them
            - Provide specific numbers for support and resistance levels and entry/exit points, not just general statements
            - Focus on providing detailed and useful analysis while maintaining important details
            - Use bold text for important information and key price levels
            - Structure your analysis with clear sections and bullet points
            - For each timeframe, include at least 2-3 specific support and resistance levels with exact numbers
            - Include specific trading strategy recommendations with exact entry, exit, and stop-loss prices
            - Provide a clear final recommendation (Buy, Sell, or Hold) with specific price targets
            - Include flag emojis for each currency (e.g., 🇺🇸 for USD, 🇸🇦 for SAR)
            """

        # استدعاء نموذج Gemini
        logger.info(f"جاري استدعاء نموذج Gemini للتحليل متعدد الإطارات الزمنية باللغة {lang}")
        try:
            # محاولة استخدام to_thread أولاً
            response_obj = await asyncio.to_thread(
                lambda: model.generate_content(prompt)
            )
            response = response_obj.text
            logger.info(f"تم الحصول على استجابة من Gemini بطول {len(response)} حرف")
        except Exception as e:
            logger.error(f"خطأ في استدعاء Gemini API: {str(e)}")
            return None

        # تطبيق التنظيف البسيط للنصوص
        try:
            from utils.utils import simple_ai_text_cleanup
            response = simple_ai_text_cleanup(response, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة simple_ai_text_cleanup")

        # تنسيق الاستجابة وإصلاح مشكلة النقطتين في النص العربي
        if lang == 'ar':
            header = f"🔍 تحليل متعدد الإطارات الزمنية لـ {symbol}\n\n"

            # إصلاح تنسيق النص العربي (إزالة النقطتين من داخل النص العريض)
            response = response.replace("**إطار 1 ساعة:**", "**إطار 1 ساعة**")
            response = response.replace("**إطار 4 ساعات:**", "**إطار 4 ساعات**")
            response = response.replace("**إطار يوم واحد:**", "**إطار يوم واحد**")
            response = response.replace("**إطار أسبوع واحد:**", "**إطار أسبوع واحد**")
            response = response.replace("**الاتجاه:**", "**الاتجاه**")
            response = response.replace("**المؤشرات:**", "**المؤشرات**")
            response = response.replace("**الأنماط:**", "**الأنماط**")
            response = response.replace("**الخلاصة:**", "**الخلاصة**")

            # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
            response = re.sub(r'\*\*(.*?):\*\*', r'**\1**', response)
        else:
            header = f"🔍 Multi-Timeframe Analysis for {symbol}:\n\n"

        # إضافة الروابط المباشرة
        from .traditional_analysis import generate_direct_links
        direct_links = generate_direct_links(symbol)

        if lang == 'ar':
            links_text = f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
        else:
            links_text = f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

        return header + response + links_text

    except Exception as e:
        logger.error(f"خطأ في التحليل متعدد الإطارات الزمنية: {str(e)}")
        return None


def format_comprehensive_report_modern(text: str, symbol: str, lang: str = 'ar') -> str:
    """
    تنسيق حديث ومحسن للتقرير التحليلي المتكامل مع فصل واضح للعناوين

    Args:
        text: النص المراد تنسيقه
        symbol: رمز العملة
        lang: لغة التقرير

    Returns:
        النص بتنسيق حديث ومنظم مع فصل واضح للعناوين
    """
    if not text:
        return ""

    import re

    # تنظيف النص أولاً
    text = clean_gemini_response_basic(text)

    # إنشاء التقرير بالتنسيق المحسن الجديد
    if lang == 'ar':
        header = f"""
╔══════════════════════════════════════╗
║  📊 التقرير التحليلي المتكامل  ║
║           🪙 {symbol}                ║
╚══════════════════════════════════════╝

"""

        # تقسيم النص إلى أقسام مع تحسين فصل العناوين
        formatted_sections = []

        # تحسين تقسيم الأقسام مع إضافة المزيد من الرموز
        sections = re.split(r'(?=📋|🎯|💰|⚠️|📊|🔮|📈|📉|🔍|💡|⭐|🚀|📌)', text)

        for i, section in enumerate(sections):
            if not section.strip():
                continue

            # تحديد نوع القسم
            if '📋' in section[:10]:
                formatted_sections.append(format_executive_summary_card(section, lang))
            elif '🎯' in section[:10]:
                formatted_sections.append(format_recommendation_card(section, lang))
            elif '💰' in section[:10]:
                formatted_sections.append(format_trading_plan_card(section, lang))
            elif '⚠️' in section[:10]:
                formatted_sections.append(format_risk_management_card(section, lang))
            elif '📊' in section[:10]:
                formatted_sections.append(format_monitoring_card(section, lang))
            elif '🔮' in section[:10]:
                formatted_sections.append(format_forecast_card(section, lang))
            else:
                # قسم عام
                formatted_sections.append(format_general_card(section, lang))

        # دمج الأقسام مع فواصل
        separator = "\n" + "─" * 40 + "\n"
        formatted_text = separator.join(formatted_sections)

        # إضافة تذييل
        footer = f"""
{separator}
┌─────────────────────────────────────┐
│  ⚡ تم إنشاء التقرير بواسطة الذكاء الاصطناعي  │
│  📅 {get_current_time_arabic()}                │
└─────────────────────────────────────┘
"""

        return header + formatted_text + footer

    else:
        # النسخة الإنجليزية
        header = f"""
╔══════════════════════════════════════╗
║    📊 Comprehensive Analysis Report    ║
║           🪙 {symbol}                ║
╚══════════════════════════════════════╝

"""

        # نفس المنطق للإنجليزية
        formatted_sections = []
        sections = re.split(r'(?=📋|🎯|💰|⚠️|📊|🔮)', text)

        for section in sections:
            if not section.strip():
                continue

            if '📋' in section[:10]:
                formatted_sections.append(format_executive_summary_card(section, lang))
            elif '🎯' in section[:10]:
                formatted_sections.append(format_recommendation_card(section, lang))
            elif '💰' in section[:10]:
                formatted_sections.append(format_trading_plan_card(section, lang))
            elif '⚠️' in section[:10]:
                formatted_sections.append(format_risk_management_card(section, lang))
            elif '📊' in section[:10]:
                formatted_sections.append(format_monitoring_card(section, lang))
            elif '🔮' in section[:10]:
                formatted_sections.append(format_forecast_card(section, lang))
            else:
                formatted_sections.append(format_general_card(section, lang))

        separator = "\n" + "─" * 40 + "\n"
        formatted_text = separator.join(formatted_sections)

        footer = f"""
{separator}
┌─────────────────────────────────────┐
│  ⚡ Generated by AI Analysis Engine    │
│  📅 {get_current_time_english()}              │
└─────────────────────────────────────┘
"""

        return header + formatted_text + footer


def clean_gemini_response_basic(text: str) -> str:
    """
    تنظيف أساسي لاستجابة Gemini مع تطبيق التنسيق المبهر

    Args:
        text: النص المراد تنظيفه

    Returns:
        النص المنظف والمنسق
    """
    if not text:
        return ""

    import re

    # تطبيق التنظيف البسيط للنصوص
    try:
        from utils.utils import simple_ai_text_cleanup
        text = simple_ai_text_cleanup(text, 'ar')  # افتراض اللغة العربية
    except ImportError:
        logger.warning("لم يتم العثور على دالة simple_ai_text_cleanup")

    # إزالة الرموز الخاصة المشكوك فيها
    problematic_chars = ['`', '~', '<', '>', '|']
    for char in problematic_chars:
        text = text.replace(char, '')

    # إصلاح المسافات المتعددة
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    text = re.sub(r'[ \t]+', ' ', text)

    return text.strip()


def format_executive_summary_card(section: str, lang: str) -> str:
    """تنسيق بطاقة الملخص التنفيذي"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  📋 الملخص التنفيذي                ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  📋 Executive Summary               ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_recommendation_card(section: str, lang: str) -> str:
    """تنسيق بطاقة التوصية المتكاملة"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  🎯 التوصية المتكاملة              ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  🎯 Integrated Recommendation      ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_trading_plan_card(section: str, lang: str) -> str:
    """تنسيق بطاقة خطة التداول"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  💰 خطة التداول المفصلة            ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  💰 Detailed Trading Plan          ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_risk_management_card(section: str, lang: str) -> str:
    """تنسيق بطاقة إدارة المخاطر"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  ⚠️ إدارة المخاطر                  ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  ⚠️ Risk Management                ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_monitoring_card(section: str, lang: str) -> str:
    """تنسيق بطاقة نقاط المراقبة"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  📊 نقاط المراقبة الحرجة           ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  📊 Critical Monitoring Points     ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_forecast_card(section: str, lang: str) -> str:
    """تنسيق بطاقة التوقعات"""
    content = extract_section_content(section)
    if lang == 'ar':
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  🔮 التوقعات قصيرة ومتوسطة المدى   ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""
    else:
        return f"""
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  🔮 Short & Medium-term Forecasts  ┃
┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

{content}
"""


def format_general_card(section: str, lang: str) -> str:
    """تنسيق بطاقة عامة"""
    content = extract_section_content(section)
    return f"""
┌─────────────────────────────────────┐
│  📄 معلومات إضافية                 │
└─────────────────────────────────────┘

{content}
"""


def extract_section_content(section: str) -> str:
    """استخراج المحتوى من القسم وتنظيفه مع تحسين فصل العناوين"""
    import re

    # إزالة العنوان الرئيسي (الرمز التعبيري + النص العريض)
    content = re.sub(r'^[🔬📊🎯💰⚠️📋🔮📈📉🔍💡⭐🚀📌]\s*\*\*[^*]+\*\*\s*', '', section.strip())

    # تنظيف المحتوى
    content = content.strip()

    # تحسين فصل العناوين الرئيسية والفرعية
    # إضافة سطر فارغ قبل العناوين الفرعية العريضة
    content = re.sub(r'(\n|^)(\*\*[^*]+\*\*)', r'\1\n\2', content)

    # تحسين تنسيق العناوين الفرعية مع فصل أوضح
    content = re.sub(r'\*\*([^*]+)\*\*:', r'\n▸ **\1**:\n', content)
    content = re.sub(r'\*\*([^*]+)\*\*', r'\n🔹 **\1**\n', content)

    # تحسين تنسيق النقاط
    content = re.sub(r'^-\s*', '• ', content, flags=re.MULTILINE)
    content = re.sub(r'^\*\s*', '• ', content, flags=re.MULTILINE)

    # إضافة تباعد أفضل بين الفقرات
    content = re.sub(r'\n\n\n+', '\n\n', content)  # تقليل المسافات المتعددة
    content = re.sub(r'(\n• [^\n]+)(\n[^•\n▸🔹])', r'\1\n\2', content)  # إضافة مسافة بعد النقاط

    # تطبيق التنظيف البسيط على المحتوى المستخرج
    try:
        from utils.utils import simple_ai_text_cleanup
        content = simple_ai_text_cleanup(content, 'ar')
    except ImportError:
        pass

    return content.strip()


def get_current_time_arabic() -> str:
    """الحصول على الوقت الحالي بالعربية"""
    from datetime import datetime
    import pytz

    # استخدام التوقيت السعودي
    saudi_tz = pytz.timezone('Asia/Riyadh')
    now = datetime.now(saudi_tz)

    return now.strftime('%Y/%m/%d - %H:%M')


def get_current_time_english() -> str:
    """الحصول على الوقت الحالي بالإنجليزية"""
    from datetime import datetime
    import pytz

    # استخدام التوقيت السعودي
    saudi_tz = pytz.timezone('Asia/Riyadh')
    now = datetime.now(saudi_tz)

    return now.strftime('%Y/%m/%d - %H:%M')


async def create_comprehensive_report(model, symbol: str, market_data: Dict[str, Any],
                                    price_predictions: str = None, multi_timeframe_analysis: str = None,
                                    trading_strategy: str = None, lang: str = 'ar') -> Optional[str]:
    """
    دمج جميع التحليلات في تقرير شامل متكامل

    Args:
        model: نموذج Gemini
        symbol: رمز العملة
        market_data: بيانات السوق الأساسية
        price_predictions: نتائج التنبؤات السعرية
        multi_timeframe_analysis: نتائج التحليل متعدد الأطر الزمنية
        trading_strategy: نتائج استراتيجية التداول
        lang: لغة التحليل (ar أو en)

    Returns:
        تقرير شامل مدمج أو None إذا فشل التحليل
    """
    try:
        if not market_data:
            logger.warning("لا توجد بيانات كافية لإنشاء التقرير الشامل")
            return None

        # استخراج البيانات الأساسية
        price = market_data.get('price', 'غير متوفر')
        price_change = market_data.get('price_change', 'غير متوفر')
        if 'change_percent' in market_data and price_change == 'غير متوفر':
            price_change = market_data.get('change_percent', 'غير متوفر')

        currency_symbol = market_data.get('currency_symbol', '$')
        indicators = market_data.get('indicators', {})

        # تصفية المؤشرات ذات القيم الصالحة
        valid_indicators = {k: v for k, v in indicators.items() if v not in [None, 'nan', 'N/A']}

        # إنشاء سياق للنموذج حسب اللغة
        if lang == 'ar':
            prompt = f"""
            أنت خبير مالي متخصص في تحليل العملات الرقمية. قم بإنشاء تقرير تحليلي شامل ومتكامل لعملة {symbol} بناءً على التحليلات التالية:

            📊 **البيانات الأساسية:**
            - السعر الحالي: {price} {currency_symbol}
            - نسبة التغير: {price_change}%

            📈 **التنبؤات السعرية:**
            {price_predictions if price_predictions else "غير متوفرة"}

            🔍 **التحليل متعدد الأطر الزمنية:**
            {multi_timeframe_analysis if multi_timeframe_analysis else "غير متوفر"}

            📋 **استراتيجية التداول:**
            {trading_strategy if trading_strategy else "غير متوفرة"}

            المطلوب منك دمج جميع التحليلات السابقة وإنشاء تقرير شامل يشمل الأقسام التالية بالترتيب:

            📋 **الملخص التنفيذي**
            تحليل شامل للوضع الحالي للعملة، دمج نتائج التنبؤات السعرية مع التحليل متعدد الأطر الزمنية، وتقييم عام للفرص والمخاطر.

            🎯 **التوصية المتكاملة**
            توصية واضحة (شراء قوي/شراء/انتظار/بيع/بيع قوي) مع الأسباب المدعومة من جميع التحليلات ومستوى الثقة في التوصية.

            💰 **خطة التداول المفصلة**
            نقاط الدخول المثلى، الأهداف السعرية، مستويات وقف الخسارة، ونسبة المخاطرة إلى المكافأة.

            ⚠️ **إدارة المخاطر**
            تقييم مستوى المخاطر الإجمالي، العوامل التي قد تؤثر على التوقعات، ونصائح لإدارة رأس المال.

            📊 **نقاط المراقبة الحرجة**
            المستويات السعرية المهمة للمراقبة، الإشارات التي تؤكد أو تنفي التحليل، والتوقيتات المهمة للمراجعة.

            🔮 **التوقعات قصيرة ومتوسطة المدى**
            توقعات الـ 24 ساعة القادمة، توقعات الأسبوع القادم، والسيناريوهات المحتملة.

            **متطلبات التنسيق:**
            - ابدأ كل قسم بالرمز التعبيري المحدد متبوعاً بالعنوان العريض
            - اكتب محتوى مفصل وشامل تحت كل قسم
            - استخدم أرقام ومستويات محددة
            - اجعل التحليل مفيداً للمتداولين من جميع المستويات
            - ادمج جميع التحليلات الثلاث بشكل متكامل
            """
        else:
            prompt = f"""
            You are a financial expert specializing in cryptocurrency analysis. Create a comprehensive and integrated analysis report for {symbol} based on the following analyses:

            📊 **Basic Data:**
            - Current Price: {price} {currency_symbol}
            - Price Change: {price_change}%

            📈 **Price Predictions:**
            {price_predictions if price_predictions else "Not available"}

            🔍 **Multi-Timeframe Analysis:**
            {multi_timeframe_analysis if multi_timeframe_analysis else "Not available"}

            📋 **Trading Strategy:**
            {trading_strategy if trading_strategy else "Not available"}

            Please merge all the above analyses and create a comprehensive report that includes the following sections in order:

            📋 **Executive Summary**
            Comprehensive analysis of the current state of the currency, merge price prediction results with multi-timeframe analysis, and overall assessment of opportunities and risks.

            🎯 **Integrated Recommendation**
            Clear recommendation (Strong Buy/Buy/Hold/Sell/Strong Sell) with reasons supported by all analyses and confidence level in the recommendation.

            💰 **Detailed Trading Plan**
            Optimal entry points, price targets, stop-loss levels, and risk-to-reward ratio.

            ⚠️ **Risk Management**
            Overall risk level assessment, factors that may affect predictions, and capital management advice.

            📊 **Critical Monitoring Points**
            Important price levels to monitor, signals that confirm or deny the analysis, and important timings for review.

            🔮 **Short and Medium-term Forecasts**
            Next 24 hours predictions, next week predictions, and possible scenarios.

            **Formatting Requirements:**
            - Start each section with the specified emoji followed by the bold title
            - Write detailed and comprehensive content under each section
            - Use specific numbers and levels
            - Make the analysis useful for traders of all levels
            - Integrate all three analyses comprehensively
            """

        # استدعاء نموذج Gemini
        response = await model.generate_content_async(prompt)

        if not response or not response.text:
            logger.warning("لم يتم الحصول على استجابة من Gemini API للتقرير الشامل")
            return None

        # تطبيق التنسيق المبهر المحسن أولاً
        analysis_text = response.text.strip()

        try:
            from utils.utils import simple_ai_text_cleanup
            analysis_text = simple_ai_text_cleanup(analysis_text, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة simple_ai_text_cleanup")

        # تطبيق التنسيق الجديد المبتكر
        formatted_report = format_comprehensive_report_modern(analysis_text, symbol, lang)

        # تطبيق الحل المبهر مرة أخرى بعد التنسيق المتقدم
        try:
            from utils.utils import fix_bold_formatting
            formatted_report = fix_bold_formatting(formatted_report, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting للتطبيق النهائي")

        # إضافة الروابط المباشرة
        from .traditional_analysis import generate_direct_links
        direct_links = generate_direct_links(symbol)

        if lang == 'ar':
            links_text = f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
        else:
            links_text = f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

        final_report = formatted_report + links_text

        # تطبيق الحل المبهر النهائي
        try:
            from utils.utils import fix_bold_formatting
            final_report = fix_bold_formatting(final_report, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting للتطبيق النهائي")

        return final_report

    except Exception as e:
        logger.error(f"خطأ في إنشاء التقرير التحليلي الشامل: {str(e)}")
        return None


async def get_user_api_client(user_id: str, api_type: str = 'gemini'):
    """
    الحصول على عميل API للمستخدم

    Args:
        user_id: معرف المستخدم
        api_type: نوع API (gemini)

    Returns:
        عميل API أو None إذا لم يتم العثور على مفتاح صالح
    """
    try:
        if api_type != 'gemini':
            logger.error(f"نوع API غير مدعوم: {api_type}")
            return None

        # محاولة الحصول على مفتاح API من Firestore أولاً (للمستخدمين المشتركين)
        if db:
            api_ref = db.collection('user_api_keys').document(user_id)
            api_data = api_ref.get()

            if api_data.exists and 'gemini_key' in api_data.to_dict():
                logger.info(f"استخدام مفتاح Gemini API المخصص للمستخدم {user_id}")
                encrypted_key = api_data.to_dict()['gemini_key']

                # فك تشفير المفتاح
                try:
                    from api_manager import APIManager
                    # الحصول على مفتاح التشفير من Firestore
                    key_ref = db.collection('system_config').document('encryption_key')
                    key_data = key_ref.get()

                    if key_data.exists and 'key' in key_data.to_dict():
                        encryption_key = key_data.to_dict()['key']
                        # إنشاء مثيل APIManager مؤقت لفك التشفير
                        temp_api_manager = APIManager(db, encryption_key)
                        api_key = temp_api_manager.decrypt_data(encrypted_key)

                        if api_key:
                            logger.info(f"تم فك تشفير مفتاح Gemini API للمستخدم {user_id} بنجاح")
                            return _initialize_gemini_client(api_key, user_id)
                except Exception as e:
                    logger.error(f"خطأ في فك تشفير مفتاح Gemini API للمستخدم {user_id}: {str(e)}")
                    # فشل في فك تشفير المفتاح، سيتم إرجاع None

        # لم يتم العثور على مفتاح API للمستخدم
        logger.warning(f"لم يتم العثور على مفتاح Gemini API للمستخدم {user_id}")
        return None
    except Exception as e:
        logger.error(f"خطأ في الحصول على عميل API للمستخدم {user_id}: {str(e)}")
        return None


def _initialize_gemini_client(api_key: str, user_id: str, exclude_models: list = None):
    """
    تهيئة عميل Gemini باستخدام مفتاح API

    Args:
        api_key: مفتاح API لـ Gemini
        user_id: معرف المستخدم للتسجيل
        exclude_models: قائمة بالنماذج المراد استبعادها

    Returns:
        نموذج Gemini أو None في حالة الفشل
    """
    try:
        # تهيئة عميل Gemini
        import google.generativeai as genai
        genai.configure(api_key=api_key)

        if exclude_models is None:
            exclude_models = []

        # قائمة النماذج المتاحة بترتيب الأولوية (النماذج المجانية فقط)
        available_models = [
            'gemini-2.0-flash-exp',
            'gemini-2.0-flash'
        ]

        # إزالة النماذج المستبعدة
        models_to_try = [model for model in available_models if model not in exclude_models]

        for model_name in models_to_try:
            try:
                logger.info(f"محاولة استخدام نموذج {model_name} للمستخدم {user_id}")
                model = genai.GenerativeModel(model_name)
                logger.info(f"تم إنشاء نموذج {model_name} بنجاح للمستخدم {user_id}")
                return model
            except Exception as model_error:
                logger.warning(f"فشل في استخدام نموذج {model_name}: {str(model_error)}")
                continue

        logger.error(f"فشل في استخدام جميع نماذج Gemini المتاحة للمستخدم {user_id}")
        return None
    except Exception as e:
        logger.error(f"خطأ في تهيئة عميل Gemini: {str(e)}")
        return None


async def get_user_api_client_with_fallback(user_id: str, api_type: str = 'gemini', exclude_models: list = None):
    """
    الحصول على عميل API للمستخدم مع إمكانية استبعاد نماذج معينة

    Args:
        user_id: معرف المستخدم
        api_type: نوع API (افتراضي: 'gemini')
        exclude_models: قائمة بالنماذج المراد استبعادها

    Returns:
        عميل API مهيأ أو None إذا لم تكن هناك مفاتيح متاحة
    """
    try:
        # استخدام مدير API من المتغير العام
        import sys
        # استخدام مدير API من bot.py
        from firebase_admin import firestore
        from cryptography.fernet import Fernet
        db = firestore.client()

        # الحصول على مفتاح التشفير
        key_ref = db.collection('system_config').document('encryption_key')
        key_data = key_ref.get()

        if key_data.exists and 'key' in key_data.to_dict():
            encryption_key = key_data.to_dict()['key']
            cipher = Fernet(encryption_key.encode())

            # استخدام مفتاح API المخزن للمستخدم
            api_keys_ref = db.collection('user_api_keys').document(user_id)
            api_keys_doc = api_keys_ref.get()

            if api_keys_doc.exists:
                api_keys_data = api_keys_doc.to_dict()

                if api_type == 'gemini':
                    # الحصول على مفتاح API لـ Gemini
                    encrypted_key = api_keys_data.get('gemini_key')

                    if encrypted_key:
                        try:
                            # فك تشفير المفتاح
                            api_key = cipher.decrypt(encrypted_key.encode()).decode()

                            # تهيئة عميل Gemini مع استبعاد النماذج المحددة
                            import google.generativeai as genai
                            logger.info(f"تهيئة Gemini API للمستخدم {user_id} باستخدام مفتاح المستخدم مع استبعاد النماذج: {exclude_models}")
                            genai.configure(api_key=api_key)

                            # استخدام الدالة المحدثة مع استبعاد النماذج
                            model = _initialize_gemini_client(api_key, user_id, exclude_models)
                            if model:
                                logger.info(f"تم إنشاء نموذج Gemini بديل بنجاح للمستخدم {user_id}")
                                return model
                            else:
                                logger.warning(f"فشل في إنشاء نموذج Gemini بديل للمستخدم {user_id}")
                                return None

                        except Exception as e:
                            logger.error(f"خطأ في فك تشفير مفتاح Gemini API للمستخدم {user_id}: {str(e)}")
                            return None

        # لم يتم العثور على مفتاح API للمستخدم
        logger.warning(f"لم يتم العثور على مفتاح Gemini API للمستخدم {user_id}")
        return None
    except Exception as e:
        logger.error(f"خطأ في الحصول على عميل API البديل للمستخدم {user_id}: {str(e)}")
        return None


async def verify_gemini_api(api_key: str) -> Tuple[bool, str]:
    """
    التحقق من صحة مفتاح API لـ Gemini

    Args:
        api_key: مفتاح API لـ Gemini

    Returns:
        زوج (صالح، رسالة_خطأ)
    """
    try:
        # استخدام دالة تهيئة عميل Gemini
        model = _initialize_gemini_client(api_key, "verification")

        if not model:
            return False, "فشل في تهيئة نموذج Gemini"

        # استخدام to_thread لتجنب مشاكل التزامن
        try:
            response = await asyncio.to_thread(
                lambda: model.generate_content("Hello").text
            )
        except Exception as thread_error:
            logger.warning(f"فشل استدعاء to_thread: {str(thread_error)}")
            # محاولة بديلة باستخدام generate_content_async
            response = await model.generate_content_async("Hello")
            response = response.text if hasattr(response, 'text') else str(response)

        if response:
            logger.info("تم التحقق من مفتاح Gemini API بنجاح")
            return True, ""
        else:
            return False, "لم يتم الحصول على استجابة من API"

    except Exception as e:
        error_msg = str(e)
        logger.error(f"خطأ في التحقق من مفتاح Gemini API: {error_msg}")

        if "404" in error_msg:
            return False, "خطأ 404: لم يتم العثور على النموذج. تأكد من استخدام 'gemini-1.5-flash'"
        elif "401" in error_msg or "403" in error_msg:
            return False, "خطأ في المصادقة: مفتاح API غير صالح أو منتهي الصلاحية"
        elif "API key not valid" in error_msg:
            return False, "مفتاح API غير صالح"
        elif "quota exceeded" in error_msg.lower():
            return False, "تم تجاوز حصة API. يرجى المحاولة لاحقًا."
        elif "models/gemini-2.0-flash-exp is not found" in error_msg:
            return False, "نموذج gemini-2.0-flash-exp غير متاح. يرجى التأكد من أن مفتاح API يدعم نماذج Gemini 2.0"
        elif "models/gemini-1.5-flash is not found" in error_msg:
            return False, "نموذج gemini-1.5-flash غير متاح. يرجى التأكد من أن مفتاح API يدعم نماذج Gemini 1.5"
        else:
            return False, f"خطأ في التحقق من مفتاح API: {error_msg}"
