"""
نظام إدارة معدل الطلبات الذكي
يدير استخدام API بكفاءة ويوزع الطلبات بذكاء عبر اليوم
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import deque
import json
import math

logger = logging.getLogger(__name__)

@dataclass
class APIRequest:
    """طلب API"""
    platform: str
    endpoint: str
    timestamp: datetime
    success: bool = True
    response_time: float = 0.0
    error_message: Optional[str] = None

@dataclass
class PlatformLimits:
    """حدود منصة API"""
    name: str
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int = 0  # حد الطلبات المتتالية
    cooldown_seconds: int = 1  # فترة التهدئة بين الطلبات
    
    # إحصائيات الاستخدام
    current_minute_count: int = 0
    current_hour_count: int = 0
    current_day_count: int = 0
    
    # أوقات إعادة التعيين
    minute_reset: datetime = field(default_factory=datetime.now)
    hour_reset: datetime = field(default_factory=datetime.now)
    day_reset: datetime = field(default_factory=datetime.now)
    
    # تاريخ آخر طلب
    last_request: Optional[datetime] = None

class SmartRateLimiter:
    """نظام إدارة معدل الطلبات الذكي"""
    
    def __init__(self, db=None):
        self.db = db
        
        # تكوين حدود المنصات مع مراعاة gemini-2.0-flash المحسن
        self.platforms = {
            'binance': PlatformLimits(
                name='binance',
                requests_per_minute=60,
                requests_per_hour=3600,
                requests_per_day=86400,
                burst_limit=10,
                cooldown_seconds=1
            ),
            'coindesk': PlatformLimits(
                name='coindesk',
                requests_per_minute=30,
                requests_per_hour=1800,
                requests_per_day=43200,
                burst_limit=5,
                cooldown_seconds=2
            ),
            'coingecko': PlatformLimits(
                name='coingecko',
                requests_per_minute=50,
                requests_per_hour=3000,
                requests_per_day=72000,
                burst_limit=8,
                cooldown_seconds=1
            ),
            'gemini': PlatformLimits(
                name='gemini',
                requests_per_minute=60,  # gemini-2.0-flash أكثر سخاءً
                requests_per_hour=3600,
                requests_per_day=86400,
                burst_limit=15,  # يمكن معالجة طلبات متتالية أكثر
                cooldown_seconds=1
            )
        }
        
        # سجل الطلبات الأخيرة لكل منصة
        self.request_history = {platform: deque(maxlen=1000) for platform in self.platforms}
        
        # إحصائيات الأداء
        self.performance_stats = {
            platform: {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'average_response_time': 0.0,
                'peak_usage_hour': 0,
                'efficiency_score': 100.0
            } for platform in self.platforms
        }
        
        # نظام التنبؤ الذكي
        self.usage_patterns = {platform: [] for platform in self.platforms}
        
    async def can_make_request(self, platform: str, priority: str = 'normal') -> Tuple[bool, str]:
        """
        التحقق من إمكانية إجراء طلب API مع مراعاة الأولوية
        
        Args:
            platform: اسم المنصة
            priority: أولوية الطلب (low, normal, high, urgent)
            
        Returns:
            (يمكن_الطلب, سبب_الرفض)
        """
        if platform not in self.platforms:
            return False, f"منصة غير مدعومة: {platform}"
        
        platform_limits = self.platforms[platform]
        current_time = datetime.now()
        
        # تحديث العدادات
        await self._update_counters(platform, current_time)
        
        # التحقق من الحدود حسب الأولوية
        if priority == 'urgent':
            # الطلبات العاجلة لها أولوية عالية
            if platform_limits.current_minute_count >= platform_limits.requests_per_minute * 0.9:
                return False, "تم تجاوز 90% من حد الدقيقة للطلبات العاجلة"
        elif priority == 'high':
            if platform_limits.current_minute_count >= platform_limits.requests_per_minute * 0.8:
                return False, "تم تجاوز 80% من حد الدقيقة للطلبات عالية الأولوية"
        elif priority == 'normal':
            if platform_limits.current_minute_count >= platform_limits.requests_per_minute * 0.7:
                return False, "تم تجاوز 70% من حد الدقيقة للطلبات العادية"
        else:  # low priority
            if platform_limits.current_minute_count >= platform_limits.requests_per_minute * 0.5:
                return False, "تم تجاوز 50% من حد الدقيقة للطلبات منخفضة الأولوية"
        
        # التحقق من فترة التهدئة
        if platform_limits.last_request:
            time_since_last = (current_time - platform_limits.last_request).total_seconds()
            if time_since_last < platform_limits.cooldown_seconds:
                return False, f"يجب الانتظار {platform_limits.cooldown_seconds - time_since_last:.1f} ثانية"
        
        # التحقق من الحدود اليومية والساعية
        if platform_limits.current_day_count >= platform_limits.requests_per_day:
            return False, "تم تجاوز الحد اليومي"
        
        if platform_limits.current_hour_count >= platform_limits.requests_per_hour:
            return False, "تم تجاوز الحد الساعي"
        
        return True, "يمكن إجراء الطلب"
    
    async def record_request(self, platform: str, endpoint: str, success: bool = True, 
                           response_time: float = 0.0, error_message: str = None):
        """تسجيل طلب API"""
        if platform not in self.platforms:
            return
        
        current_time = datetime.now()
        platform_limits = self.platforms[platform]
        
        # إنشاء سجل الطلب
        request = APIRequest(
            platform=platform,
            endpoint=endpoint,
            timestamp=current_time,
            success=success,
            response_time=response_time,
            error_message=error_message
        )
        
        # إضافة إلى السجل
        self.request_history[platform].append(request)
        
        # تحديث العدادات
        platform_limits.current_minute_count += 1
        platform_limits.current_hour_count += 1
        platform_limits.current_day_count += 1
        platform_limits.last_request = current_time
        
        # تحديث الإحصائيات
        await self._update_performance_stats(platform, request)
        
        # حفظ في قاعدة البيانات إذا كانت متوفرة
        if self.db:
            await self._save_request_to_db(request)
    
    async def get_optimal_request_time(self, platform: str, priority: str = 'normal') -> Optional[datetime]:
        """
        الحصول على أفضل وقت لإجراء الطلب
        
        Returns:
            الوقت المثالي للطلب أو None إذا كان يمكن الطلب الآن
        """
        can_request, reason = await self.can_make_request(platform, priority)
        
        if can_request:
            return None  # يمكن الطلب الآن
        
        platform_limits = self.platforms[platform]
        current_time = datetime.now()
        
        # حساب الوقت المثالي بناءً على سبب الرفض
        if "حد الدقيقة" in reason:
            # انتظار حتى بداية الدقيقة التالية
            next_minute = current_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
            return next_minute
        elif "الانتظار" in reason:
            # انتظار انتهاء فترة التهدئة
            wait_time = platform_limits.cooldown_seconds
            return current_time + timedelta(seconds=wait_time)
        elif "الحد اليومي" in reason:
            # انتظار حتى اليوم التالي
            tomorrow = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            return tomorrow
        elif "الحد الساعي" in reason:
            # انتظار حتى الساعة التالية
            next_hour = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            return next_hour
        
        return current_time + timedelta(minutes=1)  # افتراضي
    
    async def get_platform_status(self, platform: str) -> Dict[str, Any]:
        """الحصول على حالة منصة API"""
        if platform not in self.platforms:
            return {}
        
        platform_limits = self.platforms[platform]
        current_time = datetime.now()
        
        # تحديث العدادات
        await self._update_counters(platform, current_time)
        
        # حساب النسب المئوية للاستخدام
        minute_usage = (platform_limits.current_minute_count / platform_limits.requests_per_minute) * 100
        hour_usage = (platform_limits.current_hour_count / platform_limits.requests_per_hour) * 100
        day_usage = (platform_limits.current_day_count / platform_limits.requests_per_day) * 100
        
        return {
            'platform': platform,
            'limits': {
                'per_minute': platform_limits.requests_per_minute,
                'per_hour': platform_limits.requests_per_hour,
                'per_day': platform_limits.requests_per_day
            },
            'current_usage': {
                'minute': platform_limits.current_minute_count,
                'hour': platform_limits.current_hour_count,
                'day': platform_limits.current_day_count
            },
            'usage_percentage': {
                'minute': round(minute_usage, 2),
                'hour': round(hour_usage, 2),
                'day': round(day_usage, 2)
            },
            'remaining': {
                'minute': platform_limits.requests_per_minute - platform_limits.current_minute_count,
                'hour': platform_limits.requests_per_hour - platform_limits.current_hour_count,
                'day': platform_limits.requests_per_day - platform_limits.current_day_count
            },
            'last_request': platform_limits.last_request.isoformat() if platform_limits.last_request else None,
            'performance': self.performance_stats[platform],
            'status': self._get_platform_health_status(platform)
        }
    
    def _get_platform_health_status(self, platform: str) -> str:
        """تحديد حالة صحة المنصة"""
        if platform not in self.platforms:
            return 'unknown'
        
        platform_limits = self.platforms[platform]
        stats = self.performance_stats[platform]
        
        # حساب معدل النجاح
        if stats['total_requests'] > 0:
            success_rate = (stats['successful_requests'] / stats['total_requests']) * 100
        else:
            success_rate = 100
        
        # حساب استخدام الحدود
        minute_usage = (platform_limits.current_minute_count / platform_limits.requests_per_minute) * 100
        
        if success_rate >= 95 and minute_usage < 70:
            return 'excellent'
        elif success_rate >= 90 and minute_usage < 85:
            return 'good'
        elif success_rate >= 80 and minute_usage < 95:
            return 'fair'
        else:
            return 'poor'

    async def _update_counters(self, platform: str, current_time: datetime):
        """تحديث عدادات الاستخدام"""
        platform_limits = self.platforms[platform]

        # إعادة تعيين عداد الدقيقة
        if current_time - platform_limits.minute_reset >= timedelta(minutes=1):
            platform_limits.current_minute_count = 0
            platform_limits.minute_reset = current_time.replace(second=0, microsecond=0)

        # إعادة تعيين عداد الساعة
        if current_time - platform_limits.hour_reset >= timedelta(hours=1):
            platform_limits.current_hour_count = 0
            platform_limits.hour_reset = current_time.replace(minute=0, second=0, microsecond=0)

        # إعادة تعيين عداد اليوم
        if current_time - platform_limits.day_reset >= timedelta(days=1):
            platform_limits.current_day_count = 0
            platform_limits.day_reset = current_time.replace(hour=0, minute=0, second=0, microsecond=0)

    async def _update_performance_stats(self, platform: str, request: APIRequest):
        """تحديث إحصائيات الأداء"""
        stats = self.performance_stats[platform]

        stats['total_requests'] += 1

        if request.success:
            stats['successful_requests'] += 1
        else:
            stats['failed_requests'] += 1

        # تحديث متوسط وقت الاستجابة
        if request.response_time > 0:
            current_avg = stats['average_response_time']
            total_requests = stats['total_requests']
            stats['average_response_time'] = ((current_avg * (total_requests - 1)) + request.response_time) / total_requests

        # تحديث ساعة الذروة
        current_hour = request.timestamp.hour
        if stats['peak_usage_hour'] == 0 or self._is_peak_hour(platform, current_hour):
            stats['peak_usage_hour'] = current_hour

        # حساب نقاط الكفاءة
        if stats['total_requests'] > 0:
            success_rate = (stats['successful_requests'] / stats['total_requests']) * 100
            response_score = max(0, 100 - (stats['average_response_time'] * 10))  # كلما قل الوقت كلما زادت النقاط
            stats['efficiency_score'] = (success_rate * 0.7) + (response_score * 0.3)

    def _is_peak_hour(self, platform: str, hour: int) -> bool:
        """تحديد ما إذا كانت الساعة هي ساعة ذروة"""
        # حساب عدد الطلبات في هذه الساعة من السجل
        current_hour_requests = 0
        peak_hour_requests = 0

        for request in self.request_history[platform]:
            if request.timestamp.hour == hour:
                current_hour_requests += 1
            elif request.timestamp.hour == self.performance_stats[platform]['peak_usage_hour']:
                peak_hour_requests += 1

        return current_hour_requests > peak_hour_requests

    async def _save_request_to_db(self, request: APIRequest):
        """حفظ طلب API في قاعدة البيانات"""
        if not self.db:
            return

        try:
            request_data = {
                'platform': request.platform,
                'endpoint': request.endpoint,
                'timestamp': request.timestamp.isoformat(),
                'success': request.success,
                'response_time': request.response_time,
                'error_message': request.error_message,
                'date': request.timestamp.date().isoformat(),
                'hour': request.timestamp.hour
            }

            # حفظ في مجموعة api_requests
            doc_id = f"{request.platform}_{int(request.timestamp.timestamp())}"
            self.db.collection('api_requests').document(doc_id).set(request_data)

        except Exception as e:
            logger.error(f"خطأ في حفظ طلب API: {str(e)}")

    async def get_usage_analytics(self, platform: str = None, days: int = 7) -> Dict[str, Any]:
        """الحصول على تحليلات الاستخدام"""
        if not self.db:
            return {}

        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # إنشاء الاستعلام
            query = self.db.collection('api_requests').where(
                'timestamp', '>=', cutoff_date.isoformat()
            )

            if platform:
                query = query.where('platform', '==', platform)

            docs = query.get()

            # تحليل البيانات
            analytics = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'platforms': {},
                'hourly_distribution': {str(i): 0 for i in range(24)},
                'daily_distribution': {},
                'average_response_time': 0.0,
                'peak_hours': [],
                'error_patterns': {}
            }

            total_response_time = 0
            response_count = 0

            for doc in docs:
                data = doc.to_dict()
                analytics['total_requests'] += 1

                platform_name = data.get('platform', 'unknown')
                if platform_name not in analytics['platforms']:
                    analytics['platforms'][platform_name] = {
                        'total': 0, 'successful': 0, 'failed': 0
                    }

                analytics['platforms'][platform_name]['total'] += 1

                if data.get('success', False):
                    analytics['successful_requests'] += 1
                    analytics['platforms'][platform_name]['successful'] += 1
                else:
                    analytics['failed_requests'] += 1
                    analytics['platforms'][platform_name]['failed'] += 1

                    # تحليل أنماط الأخطاء
                    error_msg = data.get('error_message', 'unknown_error')
                    if error_msg not in analytics['error_patterns']:
                        analytics['error_patterns'][error_msg] = 0
                    analytics['error_patterns'][error_msg] += 1

                # التوزيع الساعي
                hour = str(data.get('hour', 0))
                analytics['hourly_distribution'][hour] += 1

                # التوزيع اليومي
                date = data.get('date', datetime.now().date().isoformat())
                if date not in analytics['daily_distribution']:
                    analytics['daily_distribution'][date] = 0
                analytics['daily_distribution'][date] += 1

                # وقت الاستجابة
                response_time = data.get('response_time', 0)
                if response_time > 0:
                    total_response_time += response_time
                    response_count += 1

            # حساب المتوسطات
            if response_count > 0:
                analytics['average_response_time'] = total_response_time / response_count

            # تحديد ساعات الذروة
            hourly_sorted = sorted(analytics['hourly_distribution'].items(),
                                 key=lambda x: x[1], reverse=True)
            analytics['peak_hours'] = [hour for hour, count in hourly_sorted[:3]]

            return analytics

        except Exception as e:
            logger.error(f"خطأ في تحليل الاستخدام: {str(e)}")
            return {}

    async def optimize_request_distribution(self, platform: str) -> Dict[str, Any]:
        """تحسين توزيع الطلبات"""
        analytics = await self.get_usage_analytics(platform, days=7)

        if not analytics:
            return {}

        # تحليل أنماط الاستخدام
        hourly_dist = analytics.get('hourly_distribution', {})
        peak_hours = analytics.get('peak_hours', [])

        # اقتراحات التحسين
        recommendations = {
            'platform': platform,
            'current_efficiency': self.performance_stats[platform]['efficiency_score'],
            'peak_hours': peak_hours,
            'recommendations': [],
            'optimal_schedule': {}
        }

        # تحديد الساعات المثلى للطلبات
        low_usage_hours = []
        for hour, count in hourly_dist.items():
            if count < (analytics['total_requests'] / 24) * 0.5:  # أقل من 50% من المتوسط
                low_usage_hours.append(int(hour))

        recommendations['optimal_schedule'] = {
            'high_priority_hours': low_usage_hours[:8],  # أفضل 8 ساعات
            'medium_priority_hours': low_usage_hours[8:16],
            'avoid_hours': [int(h) for h in peak_hours]
        }

        # اقتراحات محددة
        if analytics['failed_requests'] > analytics['total_requests'] * 0.1:
            recommendations['recommendations'].append(
                "معدل الفشل مرتفع - يُنصح بتقليل معدل الطلبات"
            )

        if analytics['average_response_time'] > 5.0:
            recommendations['recommendations'].append(
                "وقت الاستجابة بطيء - يُنصح بتوزيع الطلبات على فترات أطول"
            )

        return recommendations

# إنشاء نسخة عامة من النظام
smart_rate_limiter = None

def initialize_smart_rate_limiter(db=None):
    """تهيئة نظام إدارة معدل الطلبات الذكي"""
    global smart_rate_limiter
    smart_rate_limiter = SmartRateLimiter(db)
    logger.info("✅ تم تهيئة نظام إدارة معدل الطلبات الذكي")
    return smart_rate_limiter
