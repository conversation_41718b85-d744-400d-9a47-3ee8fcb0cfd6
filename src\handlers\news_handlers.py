"""
معالجات واجهة المستخدم لنظام الأخبار
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode
from datetime import datetime
from typing import List

from services.news_system import news_system, NewsType, NewsItem
from utils.text_helpers import get_text

# استيراد subscription_system بشكل آمن
try:
    from services.subscription_system import subscription_system
except ImportError:
    subscription_system = None

# إعداد التسجيل
logger = logging.getLogger(__name__)

async def show_news_menu(update: Update, context: CallbackContext):
    """عرض قائمة الأخبار الرئيسية"""
    try:
        user_id = str(update.effective_user.id)
        
        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')
        
        # التحقق من الاشتراك أو اليوم المجاني
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)
        
        if lang == 'ar':
            title = "📰 *الأخبار والتحليلات*"
            if is_premium:
                description = "اختر نوع الأخبار التي تريد مشاهدتها:"
            else:
                description = "الأخبار المتقدمة متاحة للمشتركين فقط. يمكنك مشاهدة الأخبار العامة مجاناً."
        else:
            title = "📰 *News & Analysis*"
            if is_premium:
                description = "Choose the type of news you want to view:"
            else:
                description = "Advanced news features are available for subscribers only. You can view general news for free."
        
        # إنشاء الأزرار
        keyboard = []
        
        if is_premium:
            if lang == 'ar':
                keyboard.extend([
                    [InlineKeyboardButton("📈 توقعات الأسعار", callback_data="news_price_predictions")],
                    [InlineKeyboardButton("🆕 تنبيهات العملات الجديدة", callback_data="news_new_coins")],
                    [InlineKeyboardButton("📊 الأخبار العامة", callback_data="news_general")],
                    [InlineKeyboardButton("🔄 تحديث الأخبار", callback_data="news_refresh")]
                ])
            else:
                keyboard.extend([
                    [InlineKeyboardButton("📈 Price Predictions", callback_data="news_price_predictions")],
                    [InlineKeyboardButton("🆕 New Coin Alerts", callback_data="news_new_coins")],
                    [InlineKeyboardButton("📊 General News", callback_data="news_general")],
                    [InlineKeyboardButton("🔄 Refresh News", callback_data="news_refresh")]
                ])
        else:
            if lang == 'ar':
                keyboard.extend([
                    [InlineKeyboardButton("📊 الأخبار العامة (مجاني)", callback_data="news_general_free")],
                    [InlineKeyboardButton("💎 الاشتراك للمزيد", callback_data="subscription_menu")]
                ])
            else:
                keyboard.extend([
                    [InlineKeyboardButton("📊 General News (Free)", callback_data="news_general_free")],
                    [InlineKeyboardButton("💎 Subscribe for More", callback_data="subscription_menu")]
                ])
        
        # زر العودة
        if lang == 'ar':
            keyboard.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")])
        else:
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message_text = f"{title}\n\n{description}"
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
    except Exception as e:
        logger.error(f"خطأ في عرض قائمة الأخبار: {str(e)}")
        error_msg = "حدث خطأ في عرض الأخبار" if lang == 'ar' else "Error displaying news"
        if update.callback_query:
            await update.callback_query.answer(error_msg, show_alert=True)
        else:
            await update.message.reply_text(error_msg)

async def show_price_predictions(update: Update, context: CallbackContext):
    """عرض توقعات الأسعار"""
    try:
        user_id = str(update.effective_user.id)
        
        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')
        
        # التحقق من الصلاحيات
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)
        
        if not is_premium:
            error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
            await update.callback_query.answer(error_msg, show_alert=True)
            return
        
        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري تحليل الأخبار وتوقعات الأسعار..." if lang == 'ar' else "🔄 Analyzing news and price predictions..."
        await update.callback_query.answer(loading_msg)
        
        # الحصول على توقعات الأسعار
        if news_system:
            predictions = await news_system.get_price_predictions(lang=lang, limit=5)
        else:
            predictions = []
        
        if not predictions:
            no_news_msg = "لا توجد توقعات أسعار متاحة حالياً" if lang == 'ar' else "No price predictions available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back", 
                        callback_data="news_menu"
                    )
                ]])
            )
            return
        
        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "📈 *توقعات الأسعار*\n\n"
        else:
            message_text = "📈 *Price Predictions*\n\n"
        
        for i, prediction in enumerate(predictions[:3], 1):
            message_text += format_news_item(prediction, lang, show_analysis=True)
            if i < len(predictions[:3]):
                message_text += "\n" + "─" * 30 + "\n\n"
        
        # إضافة أزرار
        keyboard = []
        if len(predictions) > 3:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("📄 عرض المزيد", callback_data="news_more_predictions")])
            else:
                keyboard.append([InlineKeyboardButton("📄 Show More", callback_data="news_more_predictions")])
        
        keyboard.append([InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back", 
            callback_data="news_menu"
        )])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
        
    except Exception as e:
        logger.error(f"خطأ في عرض توقعات الأسعار: {str(e)}")
        error_msg = "حدث خطأ في جلب توقعات الأسعار" if lang == 'ar' else "Error fetching price predictions"
        await update.callback_query.answer(error_msg, show_alert=True)

async def show_new_coin_alerts(update: Update, context: CallbackContext):
    """عرض تنبيهات العملات الجديدة"""
    try:
        user_id = str(update.effective_user.id)
        
        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')
        
        # التحقق من الصلاحيات
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)
        
        if not is_premium:
            error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
            await update.callback_query.answer(error_msg, show_alert=True)
            return
        
        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري البحث عن العملات الجديدة..." if lang == 'ar' else "🔄 Searching for new coins..."
        await update.callback_query.answer(loading_msg)
        
        # الحصول على تنبيهات العملات الجديدة
        if news_system:
            new_coins = await news_system.get_new_coin_alerts(lang=lang, limit=3)
        else:
            new_coins = []
        
        if not new_coins:
            no_news_msg = "لا توجد عملات جديدة متاحة حالياً" if lang == 'ar' else "No new coins available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back", 
                        callback_data="news_menu"
                    )
                ]])
            )
            return
        
        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "🆕 *تنبيهات العملات الجديدة*\n\n"
        else:
            message_text = "🆕 *New Coin Alerts*\n\n"
        
        for i, coin_news in enumerate(new_coins, 1):
            message_text += format_news_item(coin_news, lang, show_analysis=True)
            if i < len(new_coins):
                message_text += "\n" + "─" * 30 + "\n\n"
        
        # إضافة أزرار
        keyboard = [[InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back", 
            callback_data="news_menu"
        )]]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
        
    except Exception as e:
        logger.error(f"خطأ في عرض تنبيهات العملات الجديدة: {str(e)}")
        error_msg = "حدث خطأ في جلب تنبيهات العملات الجديدة" if lang == 'ar' else "Error fetching new coin alerts"
        await update.callback_query.answer(error_msg, show_alert=True)

def format_news_item(news_item: NewsItem, lang: str = 'ar', show_analysis: bool = False) -> str:
    """تنسيق عنصر الخبر للعرض"""
    try:
        # تنسيق التاريخ
        time_ago = get_time_ago(news_item.published_at, lang)
        
        # رمز المشاعر
        sentiment_emoji = {
            'positive': '📈',
            'negative': '📉',
            'neutral': '➡️'
        }.get(news_item.sentiment, '➡️')
        
        # تنسيق الرسالة
        if lang == 'ar':
            formatted_text = f"{sentiment_emoji} *{news_item.title}*\n"
            formatted_text += f"📅 {time_ago}\n"
            formatted_text += f"📰 المصدر: {news_item.source.value}\n"
            
            if news_item.symbols:
                formatted_text += f"💰 العملات: {', '.join(news_item.symbols)}\n"
            
            if show_analysis and news_item.ai_analysis:
                formatted_text += f"\n🤖 *التحليل:*\n{news_item.ai_analysis[:200]}...\n"
            
            if news_item.trading_recommendation and news_item.trading_recommendation != 'HOLD':
                action_emoji = {'BUY': '🟢', 'SELL': '🔴'}.get(news_item.trading_recommendation, '🟡')
                confidence_percent = int(news_item.confidence_score * 100) if news_item.confidence_score else 0
                formatted_text += f"\n{action_emoji} *التوصية:* {news_item.trading_recommendation} ({confidence_percent}%)\n"
        else:
            formatted_text = f"{sentiment_emoji} *{news_item.title}*\n"
            formatted_text += f"📅 {time_ago}\n"
            formatted_text += f"📰 Source: {news_item.source.value}\n"
            
            if news_item.symbols:
                formatted_text += f"💰 Coins: {', '.join(news_item.symbols)}\n"
            
            if show_analysis and news_item.ai_analysis:
                formatted_text += f"\n🤖 *Analysis:*\n{news_item.ai_analysis[:200]}...\n"
            
            if news_item.trading_recommendation and news_item.trading_recommendation != 'HOLD':
                action_emoji = {'BUY': '🟢', 'SELL': '🔴'}.get(news_item.trading_recommendation, '🟡')
                confidence_percent = int(news_item.confidence_score * 100) if news_item.confidence_score else 0
                formatted_text += f"\n{action_emoji} *Recommendation:* {news_item.trading_recommendation} ({confidence_percent}%)\n"
        
        return formatted_text
        
    except Exception as e:
        logger.error(f"خطأ في تنسيق الخبر: {str(e)}")
        return news_item.title if news_item.title else "خبر غير متاح"

async def show_general_news(update: Update, context: CallbackContext, is_free: bool = False):
    """عرض الأخبار العامة"""
    try:
        user_id = str(update.effective_user.id)

        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # التحقق من الصلاحيات للنسخة المدفوعة
        if not is_free:
            is_premium = False
            if subscription_system:
                is_premium = subscription_system.is_subscribed_sync(user_id)
                if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                    is_premium = subscription_system.free_day_system.has_active_free_day(user_id)

            if not is_premium:
                error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
                await update.callback_query.answer(error_msg, show_alert=True)
                return

        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري جلب آخر الأخبار..." if lang == 'ar' else "🔄 Fetching latest news..."
        await update.callback_query.answer(loading_msg)

        # الحصول على الأخبار العامة
        if news_system:
            if is_free:
                # نسخة مجانية - أخبار بدون تحليل AI
                general_news = await news_system.get_latest_news(limit=5)
            else:
                # نسخة مدفوعة - أخبار مع تحليل AI
                general_news = await news_system.get_analyzed_news(NewsType.GENERAL_CRYPTO, limit=5)
        else:
            general_news = []

        if not general_news:
            no_news_msg = "لا توجد أخبار متاحة حالياً" if lang == 'ar' else "No news available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data="news_menu"
                    )
                ]])
            )
            return

        # تنسيق الرسالة
        if lang == 'ar':
            if is_free:
                message_text = "📊 *الأخبار العامة (مجاني)*\n\n"
            else:
                message_text = "📊 *الأخبار العامة*\n\n"
        else:
            if is_free:
                message_text = "📊 *General News (Free)*\n\n"
            else:
                message_text = "📊 *General News*\n\n"

        for i, news in enumerate(general_news[:3], 1):
            message_text += format_news_item(news, lang, show_analysis=not is_free)
            if i < len(general_news[:3]):
                message_text += "\n" + "─" * 30 + "\n\n"

        # إضافة أزرار
        keyboard = []
        if len(general_news) > 3:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("📄 عرض المزيد", callback_data="news_more_general")])
            else:
                keyboard.append([InlineKeyboardButton("📄 Show More", callback_data="news_more_general")])

        if is_free:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("💎 الاشتراك للمزيد", callback_data="subscription_menu")])
            else:
                keyboard.append([InlineKeyboardButton("💎 Subscribe for More", callback_data="subscription_menu")])

        keyboard.append([InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back",
            callback_data="news_menu"
        )])

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض الأخبار العامة: {str(e)}")
        error_msg = "حدث خطأ في جلب الأخبار" if lang == 'ar' else "Error fetching news"
        await update.callback_query.answer(error_msg, show_alert=True)

async def handle_news_callback(update: Update, context: CallbackContext):
    """معالج callbacks الأخبار"""
    try:
        query = update.callback_query
        data = query.data

        if data == "news_menu":
            await show_news_menu(update, context)
        elif data == "news_price_predictions":
            await show_price_predictions(update, context)
        elif data == "news_new_coins":
            await show_new_coin_alerts(update, context)
        elif data == "news_general":
            await show_general_news(update, context, is_free=False)
        elif data == "news_general_free":
            await show_general_news(update, context, is_free=True)
        elif data == "news_refresh":
            # إعادة تحميل الأخبار
            await show_news_menu(update, context)
        else:
            # معالجة callbacks أخرى
            pass

    except Exception as e:
        logger.error(f"خطأ في معالجة callback الأخبار: {str(e)}")

def get_time_ago(published_at: datetime, lang: str = 'ar') -> str:
    """حساب الوقت المنقضي منذ نشر الخبر"""
    try:
        now = datetime.now()
        if published_at.tzinfo:
            import pytz
            now = now.replace(tzinfo=pytz.UTC)

        diff = now - published_at

        if diff.days > 0:
            if lang == 'ar':
                return f"منذ {diff.days} يوم"
            else:
                return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            if lang == 'ar':
                return f"منذ {hours} ساعة"
            else:
                return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            if lang == 'ar':
                return f"منذ {minutes} دقيقة"
            else:
                return f"{minutes} minutes ago"
        else:
            if lang == 'ar':
                return "منذ لحظات"
            else:
                return "moments ago"

    except Exception as e:
        logger.error(f"خطأ في حساب الوقت: {str(e)}")
        return "غير معروف" if lang == 'ar' else "unknown"
