# 🛠️ أوامر المطور في نظام TradingTelegram

تنبيه للذكاء الصناعي لا تلمس الكود المتعلق باوامر المطور

## 📝 نظرة عامة
هذا الدليل يوضح جميع الأوامر المتاحة للمطور في نظام TradingTelegram. هذه الأوامر متاحة فقط للمستخدم المحدد في `DEVELOPER_ID` في إعدادات النظام.

## ⚠️ تنبيهات مهمة
- جميع هذه الأوامر تؤثر على النظام بشكل مباشر
- يجب استخدامها بحذر وفهم تام لتأثيرها
- يتم تسجيل جميع استخدامات هذه الأوامر في سجلات النظام
- يجب التأكد من صحة المعاملات قبل تنفيذها

## 📋 قائمة الأوامر

### 1. `/stats` - عرض إحصائيات المستخدمين
**الوصف**: يعرض إحصائيات تفصيلية عن مستخدمي البوت
```
المعلومات المعروضة:
• إجمالي عدد المستخدمين
• عدد المشتركين النشطين
• عدد المستخدمين المجانيين
• إحصائيات استخدام الميزات
```

### 2. `/set_subscription [user_id] [status]` - تغيير حالة اشتراك مستخدم
**الوصف**: تغيير حالة اشتراك مستخدم معين
```
المعاملات:
• user_id: معرف المستخدم (أو "me" للإشارة إلى المطور نفسه)
• status: الحالة الجديدة (active/inactive أو مشترك/غير مشترك)

ملاحظات:
• يمكن استخدام "me" بدلاً من معرف المستخدم للإشارة إلى المطور نفسه
• يمكن استخدام هذا الأمر لاستعادة حالة الاشتراك بعد استخدام أمر `/reset`

مثال:
/set_subscription 123456789 مشترك
/set_subscription me active
```

### 3. `/stop_tasks` - إيقاف المهام المجدولة
**الوصف**: إيقاف جميع المهام المجدولة في البوت
```
المهام التي يتم إيقافها:
• فحص التنبيهات (كل 5 دقائق)
• إرسال التقارير الدورية (كل ساعة)
• النسخ الاحتياطي التلقائي (كل 24 ساعة)
• تنظيف المعاملات المعلقة (كل ساعة)
• فحص الاشتراكات المنتهية (كل ساعة)
```

### 4. `/reset [user_id]` - إعادة تعيين بيانات مستخدم
**الوصف**: إعادة تعيين بيانات مستخدم معين
```
ما يتم إعادة تعيينه:
• عدد التحليلات المجانية
• عدد التنبيهات المجانية
• إعدادات المستخدم المخصصة

ملاحظات:
• إذا لم يتم تحديد معرف المستخدم، سيتم إعادة تعيين بيانات المطور نفسه
• عند إعادة تعيين بيانات المطور، سيتم تغيير حالة اشتراكه مؤقتًا إلى "غير مشترك"
• يمكن استعادة حالة الاشتراك باستخدام أمر `/set_subscription me active`

مثال:
/reset 123456789
/reset (لإعادة تعيين بيانات المطور نفسه)
```

### 5. `/backup` - إنشاء نسخة احتياطية
**الوصف**: إنشاء نسخة احتياطية مشفرة من قاعدة البيانات
```
المجموعات المنسوخة:
• إعدادات المستخدمين
• الاشتراكات
• التنبيهات
• التقارير الدورية
• الإحصائيات
• الإعدادات العامة

ملاحظات:
• يتم تشفير النسخة قبل حفظها
• يتم إرسال مفتاح التشفير بشكل منفصل
• يتم حفظ النسخة في GitHub
```

### 6. `/cast [message]` - إرسال رسالة جماعية
**الوصف**: إرسال رسالة لجميع مستخدمي البوت
```
المميزات:
• دعم تنسيق Markdown
• تتبع نجاح/فشل الإرسال
• إحصائيات الإرسال

مثال:
/broadcast مرحباً بكم في التحديث الجديد! 🎉
```

### 7. `/ban [user_id]` - حظر مستخدم
**الوصف**: حظر مستخدم من استخدام البوت
```
المعلومات المسجلة:
• وقت الحظر
• المطور الذي قام بالحظر
• سبب الحظر (اختياري)

مثال:
/ban 123456789
```

### 8. `/unban [user_id]` - إلغاء حظر مستخدم
**الوصف**: إلغاء حظر مستخدم محظور
```
العملية:
• حذف المستخدم من قائمة المحظورين
• السماح له باستخدام البوت مجدداً

مثال:
/unban 123456789
```

### 9. `/system_info` - عرض معلومات النظام
**الوصف**: عرض معلومات تفصيلية عن حالة النظام
```
المعلومات المعروضة:
• حالة اتصال Firestore
• حالة اتصال Redis
• إجمالي المستخدمين
• عدد المشتركين النشطين
• عدد التنبيهات النشطة
• إصدار النظام
• وقت التشغيل
```

### 10. `/cleanup` - تنظيف النظام
**الوصف**: تنظيف البيانات القديمة والغير ضرورية
```
ما يتم تنظيفه:
• المعاملات الفاشلة
• النسخ الاحتياطية القديمة
• التنبيهات منتهية الصلاحية
• البيانات المؤقتة
```

### 11. `/free_day [user_id|all] [duration_hours]` - منح يوم مجاني
**الوصف**: منح يوم مجاني للمستخدمين لاستخدام المميزات المدفوعة
```
المعاملات:
• user_id: معرف المستخدم المراد منحه يوم مجاني
• all: لمنح يوم مجاني لجميع المستخدمين غير المشتركين
• duration_hours: (اختياري) مدة اليوم المجاني بالساعات (افتراضياً 24 ساعة)

ملاحظات:
• يمكن استخدام هذا الأمر لمنح المستخدمين فترة تجريبية للميزات المدفوعة
• عند استخدام "all"، سيتم تخطي المستخدمين المشتركين بالفعل

مثال:
/free_day 123456789
/free_day all
/free_day 123456789 48
```

## 🔒 الأمان والصلاحيات

### التحقق من الصلاحيات
```python
if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
    return
```

### تسجيل الاستخدام
- يتم تسجيل جميع استخدامات أوامر المطور في سجلات النظام
- يتم تسجيل الوقت والمستخدم والأمر المستخدم
- يتم تسجيل نتيجة تنفيذ الأمر (نجاح/فشل)

## 📊 أفضل الممارسات

1. **التحقق قبل التنفيذ**
   - تأكد من صحة معرف المستخدم قبل تنفيذ الأوامر
   - تحقق من حالة النظام قبل تنفيذ العمليات الحساسة

2. **النسخ الاحتياطي**
   - قم بإنشاء نسخة احتياطية قبل تنفيذ تغييرات كبيرة
   - احتفظ بمفاتيح التشفير في مكان آمن

3. **المراقبة**
   - راقب سجلات النظام بانتظام
   - تحقق من الإحصائيات بشكل دوري

4. **الأمان**
   - لا تشارك صلاحيات المطور مع أي شخص
   - غير كلمات المرور ومفاتيح API بشكل دوري

## 🔧 حل المشكلات الشائعة

1. **خطأ في الاتصال بقواعد البيانات**
   ```
   • تحقق من إعدادات الاتصال
   • تأكد من صلاحية مفاتيح API
   • راجع سجلات الخطأ
   ```

2. **فشل في إرسال الرسائل الجماعية**
   ```
   • تحقق من قيود Telegram API
   • تأكد من صلاحيات البوت
   • راجع تنسيق الرسالة
   ```

3. **مشاكل في النسخ الاحتياطي**
   ```
   • تحقق من مساحة التخزين
   • تأكد من صلاحيات GitHub
   • راجع إعدادات التشفير
   ```
