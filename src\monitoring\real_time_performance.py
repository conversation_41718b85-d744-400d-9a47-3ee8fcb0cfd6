"""
ملف بديل فارغ لمراقب الأداء
تم حذف مراقبة الأداء لأن الاستضافة تتولى هذه المهام
"""

class RealTimePerformanceMonitor:
    """فئة بديلة فارغة لمراقب الأداء"""
    
    def __init__(self):
        pass
    
    async def start_monitoring(self):
        """بدء المراقبة - فارغة"""
        pass
    
    async def record_request(self, **kwargs):
        """تسجيل طلب - فارغة"""
        pass
    
    async def stop_monitoring(self):
        """إيقاف المراقبة - فارغة"""
        pass
