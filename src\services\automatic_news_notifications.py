"""
نظام الإشعارات التلقائية للأخبار المهمة
يرسل إشعارات ذكية للمستخدمين عند ظهور أخبار مهمة أو عملات جديدة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import json

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """أنواع الإشعارات"""
    BREAKING_NEWS = "breaking_news"
    NEW_COIN = "new_coin"
    PRICE_ALERT = "price_alert"
    MARKET_ANALYSIS = "market_analysis"
    DAILY_SUMMARY = "daily_summary"
    URGENT_UPDATE = "urgent_update"

class NotificationPriority(Enum):
    """أولوية الإشعارات"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4

@dataclass
class NotificationRule:
    """قاعدة إشعار"""
    user_id: str
    notification_type: NotificationType
    keywords: List[str] = None
    symbols: List[str] = None
    min_priority: NotificationPriority = NotificationPriority.MEDIUM
    enabled: bool = True
    created_at: datetime = None
    last_triggered: datetime = None

@dataclass
class NotificationMessage:
    """رسالة إشعار"""
    user_id: str
    notification_type: NotificationType
    priority: NotificationPriority
    title: str
    content: str
    data: Dict[str, Any] = None
    created_at: datetime = None
    sent_at: datetime = None
    read_at: datetime = None

class AutomaticNewsNotifications:
    """نظام الإشعارات التلقائية للأخبار"""
    
    def __init__(self, db=None, bot=None):
        self.db = db
        self.bot = bot  # Telegram bot instance
        
        # قواعد الإشعارات للمستخدمين
        self.user_rules: Dict[str, List[NotificationRule]] = {}
        
        # قائمة انتظار الإشعارات
        self.notification_queue: List[NotificationMessage] = []
        
        # إحصائيات الإشعارات
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'by_type': {ntype.value: 0 for ntype in NotificationType},
            'by_priority': {priority.value: 0 for priority in NotificationPriority}
        }
        
        # الكلمات المفتاحية للأخبار المهمة
        self.breaking_news_keywords = [
            # إنجليزي
            'breaking', 'urgent', 'alert', 'crash', 'surge', 'pump', 'dump',
            'hack', 'exploit', 'regulation', 'ban', 'approval', 'launch',
            'partnership', 'acquisition', 'listing', 'delisting',
            # عربي
            'عاجل', 'تحذير', 'انهيار', 'ارتفاع', 'هبوط', 'اختراق',
            'تنظيم', 'حظر', 'موافقة', 'إطلاق', 'شراكة', 'استحواذ', 'إدراج'
        ]
        
        # الكلمات المفتاحية للعملات الجديدة
        self.new_coin_keywords = [
            'new listing', 'new token', 'new coin', 'launch', 'debut',
            'إدراج جديد', 'عملة جديدة', 'رمز جديد', 'إطلاق', 'ظهور'
        ]
        
        # حدود الإرسال لتجنب الإزعاج
        self.rate_limits = {
            NotificationPriority.LOW: timedelta(hours=4),
            NotificationPriority.MEDIUM: timedelta(hours=2),
            NotificationPriority.HIGH: timedelta(minutes=30),
            NotificationPriority.URGENT: timedelta(minutes=5)
        }
        
        # سجل الإشعارات المرسلة لتجنب التكرار
        self.sent_notifications: Set[str] = set()
    
    async def add_user_rule(self, user_id: str, notification_type: NotificationType,
                           keywords: List[str] = None, symbols: List[str] = None,
                           min_priority: NotificationPriority = NotificationPriority.MEDIUM) -> bool:
        """إضافة قاعدة إشعار للمستخدم"""
        try:
            rule = NotificationRule(
                user_id=user_id,
                notification_type=notification_type,
                keywords=keywords or [],
                symbols=symbols or [],
                min_priority=min_priority,
                created_at=datetime.now()
            )
            
            if user_id not in self.user_rules:
                self.user_rules[user_id] = []
            
            self.user_rules[user_id].append(rule)
            
            # حفظ في قاعدة البيانات
            if self.db:
                await self._save_user_rule(rule)
            
            logger.info(f"تم إضافة قاعدة إشعار للمستخدم {user_id}: {notification_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة قاعدة الإشعار: {str(e)}")
            return False
    
    async def remove_user_rule(self, user_id: str, notification_type: NotificationType) -> bool:
        """إزالة قاعدة إشعار للمستخدم"""
        try:
            if user_id in self.user_rules:
                self.user_rules[user_id] = [
                    rule for rule in self.user_rules[user_id]
                    if rule.notification_type != notification_type
                ]
                
                # حذف من قاعدة البيانات
                if self.db:
                    await self._delete_user_rule(user_id, notification_type)
                
                logger.info(f"تم إزالة قاعدة إشعار للمستخدم {user_id}: {notification_type.value}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إزالة قاعدة الإشعار: {str(e)}")
            return False
    
    async def process_news_for_notifications(self, news_items: List[Any]):
        """معالجة الأخبار لإنشاء الإشعارات"""
        try:
            for news_item in news_items:
                # تحليل الخبر لتحديد نوع الإشعار
                notification_type, priority = await self._analyze_news_importance(news_item)
                
                if notification_type:
                    # العثور على المستخدمين المهتمين
                    interested_users = await self._find_interested_users(news_item, notification_type)
                    
                    # إنشاء الإشعارات
                    for user_id in interested_users:
                        await self._create_notification(
                            user_id=user_id,
                            notification_type=notification_type,
                            priority=priority,
                            news_item=news_item
                        )
            
            # إرسال الإشعارات المتراكمة
            await self._process_notification_queue()
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الأخبار للإشعارات: {str(e)}")
    
    async def _analyze_news_importance(self, news_item) -> Tuple[Optional[NotificationType], NotificationPriority]:
        """تحليل أهمية الخبر"""
        title = news_item.title.lower()
        content = news_item.content.lower()
        
        # فحص الأخبار العاجلة
        if any(keyword in title or keyword in content for keyword in self.breaking_news_keywords):
            # تحديد مستوى الأولوية
            urgent_keywords = ['hack', 'exploit', 'crash', 'ban', 'اختراق', 'انهيار', 'حظر']
            if any(keyword in title or keyword in content for keyword in urgent_keywords):
                return NotificationType.BREAKING_NEWS, NotificationPriority.URGENT
            else:
                return NotificationType.BREAKING_NEWS, NotificationPriority.HIGH
        
        # فحص العملات الجديدة
        if any(keyword in title or keyword in content for keyword in self.new_coin_keywords):
            return NotificationType.NEW_COIN, NotificationPriority.HIGH
        
        # فحص تحليلات السوق
        analysis_keywords = ['analysis', 'prediction', 'forecast', 'تحليل', 'توقع', 'تنبؤ']
        if any(keyword in title or keyword in content for keyword in analysis_keywords):
            return NotificationType.MARKET_ANALYSIS, NotificationPriority.MEDIUM
        
        # أخبار عادية
        return None, NotificationPriority.LOW
    
    async def _find_interested_users(self, news_item, notification_type: NotificationType) -> List[str]:
        """العثور على المستخدمين المهتمين بالخبر"""
        interested_users = []
        
        for user_id, rules in self.user_rules.items():
            for rule in rules:
                if not rule.enabled:
                    continue
                
                if rule.notification_type != notification_type:
                    continue
                
                # فحص الكلمات المفتاحية
                if rule.keywords:
                    title_content = f"{news_item.title} {news_item.content}".lower()
                    if not any(keyword.lower() in title_content for keyword in rule.keywords):
                        continue
                
                # فحص الرموز
                if rule.symbols and news_item.symbols:
                    if not any(symbol in news_item.symbols for symbol in rule.symbols):
                        continue
                
                # فحص حدود الإرسال
                if await self._check_rate_limit(user_id, rule):
                    interested_users.append(user_id)
                    break  # قاعدة واحدة كافية لكل مستخدم
        
        return interested_users
    
    async def _check_rate_limit(self, user_id: str, rule: NotificationRule) -> bool:
        """فحص حدود معدل الإرسال"""
        if not rule.last_triggered:
            return True
        
        time_since_last = datetime.now() - rule.last_triggered
        min_interval = self.rate_limits.get(rule.min_priority, timedelta(hours=1))
        
        return time_since_last >= min_interval
    
    async def _create_notification(self, user_id: str, notification_type: NotificationType,
                                 priority: NotificationPriority, news_item):
        """إنشاء إشعار"""
        try:
            # إنشاء معرف فريد للإشعار لتجنب التكرار
            notification_id = f"{user_id}_{notification_type.value}_{hash(news_item.title)}"
            
            if notification_id in self.sent_notifications:
                return  # تم إرسال هذا الإشعار بالفعل
            
            # إنشاء عنوان ومحتوى الإشعار
            title, content = await self._format_notification_content(notification_type, news_item)
            
            notification = NotificationMessage(
                user_id=user_id,
                notification_type=notification_type,
                priority=priority,
                title=title,
                content=content,
                data={
                    'news_id': getattr(news_item, 'id', None),
                    'source': getattr(news_item, 'source', None),
                    'url': getattr(news_item, 'url', None),
                    'symbols': getattr(news_item, 'symbols', [])
                },
                created_at=datetime.now()
            )
            
            self.notification_queue.append(notification)
            self.sent_notifications.add(notification_id)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الإشعار: {str(e)}")
    
    async def _format_notification_content(self, notification_type: NotificationType, 
                                         news_item) -> Tuple[str, str]:
        """تنسيق محتوى الإشعار"""
        if notification_type == NotificationType.BREAKING_NEWS:
            title = "🚨 خبر عاجل"
            content = f"📰 {news_item.title}\n\n{news_item.content[:200]}..."
        
        elif notification_type == NotificationType.NEW_COIN:
            title = "🆕 عملة جديدة"
            content = f"💰 {news_item.title}\n\n{news_item.content[:200]}..."
        
        elif notification_type == NotificationType.MARKET_ANALYSIS:
            title = "📊 تحليل السوق"
            content = f"📈 {news_item.title}\n\n{news_item.content[:200]}..."
        
        else:
            title = "📢 إشعار"
            content = f"{news_item.title}\n\n{news_item.content[:200]}..."
        
        # إضافة الرموز إذا كانت متوفرة
        if hasattr(news_item, 'symbols') and news_item.symbols:
            symbols_text = " ".join([f"#{symbol}" for symbol in news_item.symbols[:3]])
            content += f"\n\n🏷️ {symbols_text}"
        
        return title, content
    
    async def _process_notification_queue(self):
        """معالجة قائمة انتظار الإشعارات"""
        if not self.notification_queue:
            return
        
        # ترتيب حسب الأولوية
        self.notification_queue.sort(key=lambda x: x.priority.value, reverse=True)
        
        sent_count = 0
        failed_count = 0
        
        for notification in self.notification_queue.copy():
            try:
                success = await self._send_notification(notification)
                if success:
                    sent_count += 1
                    self.stats['total_sent'] += 1
                    self.stats['by_type'][notification.notification_type.value] += 1
                    self.stats['by_priority'][notification.priority.value] += 1
                else:
                    failed_count += 1
                    self.stats['total_failed'] += 1
                
                # إزالة من القائمة
                self.notification_queue.remove(notification)
                
                # تأخير قصير لتجنب الإزعاج
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")
                failed_count += 1
                self.stats['total_failed'] += 1
        
        if sent_count > 0 or failed_count > 0:
            logger.info(f"تم إرسال {sent_count} إشعار، فشل {failed_count}")

    async def _send_notification(self, notification: NotificationMessage) -> bool:
        """إرسال الإشعار عبر Telegram"""
        if not self.bot:
            logger.warning("Telegram bot غير متوفر لإرسال الإشعارات")
            return False

        try:
            # تنسيق الرسالة
            message_text = f"<b>{notification.title}</b>\n\n{notification.content}"

            # إضافة أزرار إضافية إذا كانت متوفرة
            keyboard = None
            if notification.data and notification.data.get('url'):
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔗 قراءة المزيد", url=notification.data['url'])
                ]])

            # إرسال الرسالة
            await self.bot.send_message(
                chat_id=notification.user_id,
                text=message_text,
                parse_mode='HTML',
                reply_markup=keyboard,
                disable_web_page_preview=True
            )

            notification.sent_at = datetime.now()

            # حفظ في قاعدة البيانات
            if self.db:
                await self._save_notification(notification)

            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال الإشعار للمستخدم {notification.user_id}: {str(e)}")
            return False

    async def _save_user_rule(self, rule: NotificationRule):
        """حفظ قاعدة المستخدم في قاعدة البيانات"""
        if not self.db:
            return

        try:
            rule_data = {
                'user_id': rule.user_id,
                'notification_type': rule.notification_type.value,
                'keywords': rule.keywords or [],
                'symbols': rule.symbols or [],
                'min_priority': rule.min_priority.value,
                'enabled': rule.enabled,
                'created_at': rule.created_at.isoformat(),
                'last_triggered': rule.last_triggered.isoformat() if rule.last_triggered else None
            }

            doc_id = f"{rule.user_id}_{rule.notification_type.value}"
            self.db.collection('notification_rules').document(doc_id).set(rule_data, merge=True)

        except Exception as e:
            logger.error(f"خطأ في حفظ قاعدة الإشعار: {str(e)}")

    async def _delete_user_rule(self, user_id: str, notification_type: NotificationType):
        """حذف قاعدة المستخدم من قاعدة البيانات"""
        if not self.db:
            return

        try:
            doc_id = f"{user_id}_{notification_type.value}"
            self.db.collection('notification_rules').document(doc_id).delete()

        except Exception as e:
            logger.error(f"خطأ في حذف قاعدة الإشعار: {str(e)}")

    async def _save_notification(self, notification: NotificationMessage):
        """حفظ الإشعار في قاعدة البيانات"""
        if not self.db:
            return

        try:
            notification_data = {
                'user_id': notification.user_id,
                'notification_type': notification.notification_type.value,
                'priority': notification.priority.value,
                'title': notification.title,
                'content': notification.content,
                'data': notification.data or {},
                'created_at': notification.created_at.isoformat(),
                'sent_at': notification.sent_at.isoformat() if notification.sent_at else None,
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            }

            # إنشاء معرف فريد
            doc_id = f"{notification.user_id}_{int(notification.created_at.timestamp())}"
            self.db.collection('notifications').document(doc_id).set(notification_data)

        except Exception as e:
            logger.error(f"خطأ في حفظ الإشعار: {str(e)}")

    async def load_user_rules(self):
        """تحميل قواعد المستخدمين من قاعدة البيانات"""
        if not self.db:
            return

        try:
            docs = self.db.collection('notification_rules').get()

            for doc in docs:
                data = doc.to_dict()

                rule = NotificationRule(
                    user_id=data['user_id'],
                    notification_type=NotificationType(data['notification_type']),
                    keywords=data.get('keywords', []),
                    symbols=data.get('symbols', []),
                    min_priority=NotificationPriority(data.get('min_priority', 2)),
                    enabled=data.get('enabled', True),
                    created_at=datetime.fromisoformat(data['created_at']),
                    last_triggered=datetime.fromisoformat(data['last_triggered']) if data.get('last_triggered') else None
                )

                if rule.user_id not in self.user_rules:
                    self.user_rules[rule.user_id] = []

                self.user_rules[rule.user_id].append(rule)

            logger.info(f"تم تحميل قواعد الإشعارات لـ {len(self.user_rules)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في تحميل قواعد الإشعارات: {str(e)}")

    async def get_user_notifications(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """الحصول على إشعارات المستخدم"""
        if not self.db:
            return []

        try:
            query = self.db.collection('notifications').where(
                'user_id', '==', user_id
            ).order_by('created_at', direction='DESCENDING').limit(limit)

            docs = query.get()
            notifications = []

            for doc in docs:
                data = doc.to_dict()
                notifications.append(data)

            return notifications

        except Exception as e:
            logger.error(f"خطأ في جلب إشعارات المستخدم: {str(e)}")
            return []

    async def mark_notification_as_read(self, user_id: str, notification_id: str):
        """تمييز الإشعار كمقروء"""
        if not self.db:
            return

        try:
            self.db.collection('notifications').document(notification_id).update({
                'read_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"خطأ في تمييز الإشعار كمقروء: {str(e)}")

    async def get_notification_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الإشعارات"""
        return {
            **self.stats,
            'active_users': len(self.user_rules),
            'total_rules': sum(len(rules) for rules in self.user_rules.values()),
            'queue_size': len(self.notification_queue),
            'success_rate': (self.stats['total_sent'] / (self.stats['total_sent'] + self.stats['total_failed']) * 100) if (self.stats['total_sent'] + self.stats['total_failed']) > 0 else 0
        }

    async def send_daily_summary(self):
        """إرسال الملخص اليومي للمستخدمين المشتركين"""
        try:
            # العثور على المستخدمين المشتركين في الملخص اليومي
            daily_summary_users = []
            for user_id, rules in self.user_rules.items():
                for rule in rules:
                    if rule.notification_type == NotificationType.DAILY_SUMMARY and rule.enabled:
                        daily_summary_users.append(user_id)
                        break

            if not daily_summary_users:
                return

            # إنشاء الملخص اليومي
            summary_content = await self._generate_daily_summary()

            # إرسال للمستخدمين
            for user_id in daily_summary_users:
                notification = NotificationMessage(
                    user_id=user_id,
                    notification_type=NotificationType.DAILY_SUMMARY,
                    priority=NotificationPriority.LOW,
                    title="📊 الملخص اليومي",
                    content=summary_content,
                    created_at=datetime.now()
                )

                await self._send_notification(notification)
                await asyncio.sleep(1)  # تأخير لتجنب الإزعاج

            logger.info(f"تم إرسال الملخص اليومي لـ {len(daily_summary_users)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في إرسال الملخص اليومي: {str(e)}")

    async def _generate_daily_summary(self) -> str:
        """إنشاء الملخص اليومي"""
        # هذه الوظيفة ستحتاج إلى التكامل مع نظام الأخبار
        summary = "📊 **ملخص أخبار اليوم**\n\n"
        summary += f"📈 إجمالي الإشعارات المرسلة: {self.stats['total_sent']}\n"
        summary += f"🔥 الأخبار العاجلة: {self.stats['by_type'].get('breaking_news', 0)}\n"
        summary += f"🆕 العملات الجديدة: {self.stats['by_type'].get('new_coin', 0)}\n"
        summary += f"📊 تحليلات السوق: {self.stats['by_type'].get('market_analysis', 0)}\n"

        return summary

# إنشاء نسخة عامة من النظام
automatic_news_notifications = None

def initialize_automatic_news_notifications(db=None, bot=None):
    """تهيئة نظام الإشعارات التلقائية"""
    global automatic_news_notifications
    automatic_news_notifications = AutomaticNewsNotifications(db, bot)
    logger.info("✅ تم تهيئة نظام الإشعارات التلقائية للأخبار")
    return automatic_news_notifications
